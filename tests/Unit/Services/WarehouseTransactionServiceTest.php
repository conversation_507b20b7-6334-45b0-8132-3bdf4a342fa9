<?php

namespace Tests\Unit\Services;

use App\Contracts\Services\Internal\WarehouseTransactionServiceContract;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class WarehouseTransactionServiceTest extends TestCase
{
    use RefreshDatabase;

    private WarehouseTransactionServiceContract $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = app(WarehouseTransactionServiceContract::class);
    }

    public function test_service_is_registered(): void
    {
        $this->assertInstanceOf(
            WarehouseTransactionServiceContract::class,
            $this->service
        );
    }

    public function test_service_has_required_methods(): void
    {
        $this->assertTrue(method_exists($this->service, 'createTransaction'));
        $this->assertTrue(method_exists($this->service, 'createReceiptTransaction'));
        $this->assertTrue(method_exists($this->service, 'createIssueTransaction'));
        $this->assertTrue(method_exists($this->service, 'createReservationTransaction'));
        $this->assertTrue(method_exists($this->service, 'createAdjustmentTransaction'));
        $this->assertTrue(method_exists($this->service, 'getTransactionsByDocument'));
        $this->assertTrue(method_exists($this->service, 'getTransactionsByWarehouse'));
        $this->assertTrue(method_exists($this->service, 'getTransactionsByProduct'));
        $this->assertTrue(method_exists($this->service, 'calculateBalance'));
    }
}
