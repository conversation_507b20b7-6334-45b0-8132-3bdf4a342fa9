<?php

namespace Tests\Unit\Services\WarehouseOrderScheme;

use App\Entities\WarehouseOrderSchemeEntity;
use App\Services\Api\Internal\WarehouseOrderScheme\Handlers\SchemeDetectionHandler;
use Carbon\Carbon;
use Mockery;
use PHPUnit\Framework\TestCase;

class SchemeDetectionHandlerTest extends TestCase
{
    private SchemeDetectionHandler $handler;
    private $entityMock;

    protected function setUp(): void
    {
        parent::setUp();

        $this->entityMock = Mockery::mock(WarehouseOrderSchemeEntity::class);
        $this->handler = new SchemeDetectionHandler($this->entityMock);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_is_order_scheme_active_for_receipts_returns_false_when_no_scheme_exists(): void
    {
        $warehouseId = 'warehouse-123';

        $this->entityMock
            ->shouldReceive('where')
            ->with('warehouse_id', $warehouseId)
            ->andReturnSelf();

        $this->entityMock
            ->shouldReceive('first')
            ->andReturn(null);

        $result = $this->handler->isOrderSchemeActiveForReceipts($warehouseId);

        $this->assertFalse($result);
    }

    public function test_is_order_scheme_active_for_receipts_returns_true_when_active(): void
    {
        $warehouseId = 'warehouse-123';
        $scheme = (object) [
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => '2024-01-01',
            'control_operational_balances' => true,
        ];

        $this->entityMock
            ->shouldReceive('where')
            ->with('warehouse_id', $warehouseId)
            ->andReturnSelf();

        $this->entityMock
            ->shouldReceive('first')
            ->andReturn($scheme);

        Carbon::setTestNow('2024-01-02');
        $result = $this->handler->isOrderSchemeActiveForReceipts($warehouseId);

        $this->assertTrue($result);
        Carbon::setTestNow();
    }

    public function test_check_operation_availability_for_shipment_operation(): void
    {
        $warehouseId = 'warehouse-123';
        $operationType = 'shipment';
        
        $mode = [
            'mode' => 'shipments_only',
            'receipts_active' => false,
            'shipments_active' => true,
            'control_operational_balances' => true,
        ];

        $this->detectionServiceMock
            ->shouldReceive('getOrderSchemeMode')
            ->with($warehouseId)
            ->andReturn($mode);

        Carbon::setTestNow('2024-01-02');
        $result = $this->handler->checkOperationAvailability($warehouseId, $operationType);

        $this->assertEquals('shipment', $result['operation_type']);
        $this->assertEquals('warehouse-123', $result['warehouse_id']);
        $this->assertTrue($result['order_scheme_active']);
        $this->assertEquals('shipments_only', $result['mode']);
        $this->assertTrue($result['control_operational_balances']);
        $this->assertEquals('2024-01-02', $result['date_checked']);
        
        Carbon::setTestNow();
    }

    public function test_validate_order_scheme_operation_receipt_without_document_basis(): void
    {
        $warehouseId = 'warehouse-123';
        $operationType = 'receipt';
        $data = ['date' => '2024-01-02'];
        
        $availability = [
            'operation_type' => 'receipt',
            'warehouse_id' => 'warehouse-123',
            'order_scheme_active' => true,
            'mode' => 'receipts_only',
            'control_operational_balances' => false,
            'date_checked' => '2024-01-02',
        ];

        $mode = [
            'mode' => 'receipts_only',
            'receipts_active' => true,
            'shipments_active' => false,
            'control_operational_balances' => false,
        ];

        $this->detectionServiceMock
            ->shouldReceive('getOrderSchemeMode')
            ->with($warehouseId)
            ->andReturn($mode);

        $result = $this->handler->validateOrderSchemeOperation($warehouseId, $operationType, $data);

        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['errors']);
        $this->assertContains('Receipt operations in order scheme should have document basis', $result['warnings']);
        $this->assertEquals($availability, $result['availability']);
    }

    public function test_validate_order_scheme_operation_shipment_without_reservation_strict_control(): void
    {
        $warehouseId = 'warehouse-123';
        $operationType = 'shipment';
        $data = ['date' => '2024-01-02'];
        
        $availability = [
            'operation_type' => 'shipment',
            'warehouse_id' => 'warehouse-123',
            'order_scheme_active' => true,
            'mode' => 'shipments_only',
            'control_operational_balances' => true,
            'date_checked' => '2024-01-02',
        ];

        $mode = [
            'mode' => 'shipments_only',
            'receipts_active' => false,
            'shipments_active' => true,
            'control_operational_balances' => true,
        ];

        $this->detectionServiceMock
            ->shouldReceive('getOrderSchemeMode')
            ->with($warehouseId)
            ->andReturn($mode);

        $result = $this->handler->validateOrderSchemeOperation($warehouseId, $operationType, $data);

        $this->assertFalse($result['valid']);
        $this->assertContains('Shipment operations in order scheme require reservation', $result['errors']);
        $this->assertEmpty($result['warnings']);
        $this->assertEquals($availability, $result['availability']);
    }

    public function test_validate_order_scheme_operation_shipment_without_reservation_no_strict_control(): void
    {
        $warehouseId = 'warehouse-123';
        $operationType = 'shipment';
        $data = ['date' => '2024-01-02'];
        
        $availability = [
            'operation_type' => 'shipment',
            'warehouse_id' => 'warehouse-123',
            'order_scheme_active' => true,
            'mode' => 'shipments_only',
            'control_operational_balances' => false,
            'date_checked' => '2024-01-02',
        ];

        $mode = [
            'mode' => 'shipments_only',
            'receipts_active' => false,
            'shipments_active' => true,
            'control_operational_balances' => false,
        ];

        $this->detectionServiceMock
            ->shouldReceive('getOrderSchemeMode')
            ->with($warehouseId)
            ->andReturn($mode);

        $result = $this->handler->validateOrderSchemeOperation($warehouseId, $operationType, $data);

        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['errors']);
        $this->assertContains('Shipment operations in order scheme should have reservation', $result['warnings']);
        $this->assertEquals($availability, $result['availability']);
    }

    public function test_validate_order_scheme_operation_with_valid_data(): void
    {
        $warehouseId = 'warehouse-123';
        $operationType = 'receipt';
        $data = [
            'date' => '2024-01-02',
            'document_basis_type' => 'vendor_order',
        ];
        
        $availability = [
            'operation_type' => 'receipt',
            'warehouse_id' => 'warehouse-123',
            'order_scheme_active' => true,
            'mode' => 'receipts_only',
            'control_operational_balances' => false,
            'date_checked' => '2024-01-02',
        ];

        $mode = [
            'mode' => 'receipts_only',
            'receipts_active' => true,
            'shipments_active' => false,
            'control_operational_balances' => false,
        ];

        $this->detectionServiceMock
            ->shouldReceive('getOrderSchemeMode')
            ->with($warehouseId)
            ->andReturn($mode);

        $result = $this->handler->validateOrderSchemeOperation($warehouseId, $operationType, $data);

        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['errors']);
        $this->assertEmpty($result['warnings']);
        $this->assertEquals($availability, $result['availability']);
    }

    public function test_validate_order_scheme_operation_when_order_scheme_not_active(): void
    {
        $warehouseId = 'warehouse-123';
        $operationType = 'receipt';
        $data = ['date' => '2024-01-02'];
        
        $availability = [
            'operation_type' => 'receipt',
            'warehouse_id' => 'warehouse-123',
            'order_scheme_active' => false,
            'mode' => 'regular',
            'control_operational_balances' => false,
            'date_checked' => '2024-01-02',
        ];

        $mode = [
            'mode' => 'regular',
            'receipts_active' => false,
            'shipments_active' => false,
            'control_operational_balances' => false,
        ];

        $this->detectionServiceMock
            ->shouldReceive('getOrderSchemeMode')
            ->with($warehouseId)
            ->andReturn($mode);

        $result = $this->handler->validateOrderSchemeOperation($warehouseId, $operationType, $data);

        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['errors']);
        $this->assertEmpty($result['warnings']);
        $this->assertEquals($availability, $result['availability']);
    }
}
