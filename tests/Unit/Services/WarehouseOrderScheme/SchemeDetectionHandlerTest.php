<?php

namespace Tests\Unit\Services\WarehouseOrderScheme;

use App\Contracts\Repositories\WarehouseOrderSchemesRepositoryContract;
use App\Services\Api\Internal\WarehouseOrderScheme\Handlers\SchemeDetectionHandler;
use Carbon\Carbon;
use Mockery;
use PHPUnit\Framework\TestCase;

class SchemeDetectionHandlerTest extends TestCase
{
    private SchemeDetectionHandler $handler;
    private $repositoryMock;

    protected function setUp(): void
    {
        parent::setUp();

        $this->repositoryMock = Mockery::mock(WarehouseOrderSchemesRepositoryContract::class);
        $this->handler = new SchemeDetectionHandler($this->repositoryMock);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_is_order_scheme_active_for_receipts_returns_false_when_no_scheme_exists(): void
    {
        $warehouseId = 'warehouse-123';

        $this->repositoryMock
            ->shouldReceive('findByWarehouseId')
            ->with($warehouseId)
            ->andReturn(null);

        $result = $this->handler->isOrderSchemeActiveForReceipts($warehouseId);

        $this->assertFalse($result);
    }

    public function test_is_order_scheme_active_for_receipts_returns_true_when_active(): void
    {
        $warehouseId = 'warehouse-123';
        $scheme = (object) [
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => '2024-01-01',
            'control_operational_balances' => true,
        ];

        $this->repositoryMock
            ->shouldReceive('findByWarehouseId')
            ->with($warehouseId)
            ->andReturn($scheme);

        Carbon::setTestNow('2024-01-02');
        $result = $this->handler->isOrderSchemeActiveForReceipts($warehouseId);

        $this->assertTrue($result);
        Carbon::setTestNow();
    }

    public function test_get_order_scheme_mode_returns_full_order_scheme(): void
    {
        $warehouseId = 'warehouse-123';
        $scheme = (object) [
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => '2024-01-01',
            'control_operational_balances' => true,
        ];

        $this->repositoryMock
            ->shouldReceive('findByWarehouseId')
            ->with($warehouseId)
            ->andReturn($scheme)
            ->times(3);

        Carbon::setTestNow('2024-01-02');
        $result = $this->handler->getOrderSchemeMode($warehouseId);

        $this->assertEquals('full_order_scheme', $result['mode']);
        $this->assertTrue($result['receipts_active']);
        $this->assertTrue($result['shipments_active']);
        $this->assertTrue($result['control_operational_balances']);

        Carbon::setTestNow();
    }

    public function test_is_order_scheme_active_returns_true_for_all_when_both_active(): void
    {
        $warehouseId = 'warehouse-123';
        $scheme = (object) [
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => '2024-01-01',
            'control_operational_balances' => true,
        ];

        $this->repositoryMock
            ->shouldReceive('findByWarehouseId')
            ->with($warehouseId)
            ->andReturn($scheme)
            ->times(2);

        Carbon::setTestNow('2024-01-02');
        $result = $this->handler->isOrderSchemeActive($warehouseId, 'all');

        $this->assertTrue($result);
        Carbon::setTestNow();
    }
}
