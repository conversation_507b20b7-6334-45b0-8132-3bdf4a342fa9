<?php

namespace Tests\Unit\Services\WarehouseOrderScheme;

use App\Services\Api\Internal\WarehouseOrderScheme\Handlers\SchemeDetectionHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseOrderSchemeDetectionService;
use Carbon\Carbon;
use Mockery;
use PHPUnit\Framework\TestCase;

class SchemeDetectionHandlerTest extends TestCase
{
    private SchemeDetectionHandler $handler;
    private $detectionServiceMock;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->detectionServiceMock = Mockery::mock(WarehouseOrderSchemeDetectionService::class);
        $this->handler = new SchemeDetectionHandler($this->detectionServiceMock);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_get_warehouse_mode_returns_detection_service_result(): void
    {
        $warehouseId = 'warehouse-123';
        $expectedMode = [
            'mode' => 'full_order_scheme',
            'receipts_active' => true,
            'shipments_active' => true,
            'control_operational_balances' => true,
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => '2024-01-01',
        ];

        $this->detectionServiceMock
            ->shouldReceive('getOrderSchemeMode')
            ->with($warehouseId)
            ->andReturn($expectedMode);

        $result = $this->handler->getWarehouseMode($warehouseId);

        $this->assertEquals($expectedMode, $result);
    }

    public function test_check_operation_availability_for_receipt_operation(): void
    {
        $warehouseId = 'warehouse-123';
        $operationType = 'receipt';
        $date = '2024-01-02';
        
        $mode = [
            'mode' => 'receipts_only',
            'receipts_active' => true,
            'shipments_active' => false,
            'control_operational_balances' => false,
        ];

        $this->detectionServiceMock
            ->shouldReceive('getOrderSchemeMode')
            ->with($warehouseId)
            ->andReturn($mode);

        $result = $this->handler->checkOperationAvailability($warehouseId, $operationType, $date);

        $expected = [
            'operation_type' => 'receipt',
            'warehouse_id' => 'warehouse-123',
            'order_scheme_active' => true,
            'mode' => 'receipts_only',
            'control_operational_balances' => false,
            'date_checked' => '2024-01-02',
        ];

        $this->assertEquals($expected, $result);
    }

    public function test_check_operation_availability_for_shipment_operation(): void
    {
        $warehouseId = 'warehouse-123';
        $operationType = 'shipment';
        
        $mode = [
            'mode' => 'shipments_only',
            'receipts_active' => false,
            'shipments_active' => true,
            'control_operational_balances' => true,
        ];

        $this->detectionServiceMock
            ->shouldReceive('getOrderSchemeMode')
            ->with($warehouseId)
            ->andReturn($mode);

        Carbon::setTestNow('2024-01-02');
        $result = $this->handler->checkOperationAvailability($warehouseId, $operationType);

        $this->assertEquals('shipment', $result['operation_type']);
        $this->assertEquals('warehouse-123', $result['warehouse_id']);
        $this->assertTrue($result['order_scheme_active']);
        $this->assertEquals('shipments_only', $result['mode']);
        $this->assertTrue($result['control_operational_balances']);
        $this->assertEquals('2024-01-02', $result['date_checked']);
        
        Carbon::setTestNow();
    }

    public function test_validate_order_scheme_operation_receipt_without_document_basis(): void
    {
        $warehouseId = 'warehouse-123';
        $operationType = 'receipt';
        $data = ['date' => '2024-01-02'];
        
        $availability = [
            'operation_type' => 'receipt',
            'warehouse_id' => 'warehouse-123',
            'order_scheme_active' => true,
            'mode' => 'receipts_only',
            'control_operational_balances' => false,
            'date_checked' => '2024-01-02',
        ];

        $mode = [
            'mode' => 'receipts_only',
            'receipts_active' => true,
            'shipments_active' => false,
            'control_operational_balances' => false,
        ];

        $this->detectionServiceMock
            ->shouldReceive('getOrderSchemeMode')
            ->with($warehouseId)
            ->andReturn($mode);

        $result = $this->handler->validateOrderSchemeOperation($warehouseId, $operationType, $data);

        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['errors']);
        $this->assertContains('Receipt operations in order scheme should have document basis', $result['warnings']);
        $this->assertEquals($availability, $result['availability']);
    }

    public function test_validate_order_scheme_operation_shipment_without_reservation_strict_control(): void
    {
        $warehouseId = 'warehouse-123';
        $operationType = 'shipment';
        $data = ['date' => '2024-01-02'];
        
        $availability = [
            'operation_type' => 'shipment',
            'warehouse_id' => 'warehouse-123',
            'order_scheme_active' => true,
            'mode' => 'shipments_only',
            'control_operational_balances' => true,
            'date_checked' => '2024-01-02',
        ];

        $mode = [
            'mode' => 'shipments_only',
            'receipts_active' => false,
            'shipments_active' => true,
            'control_operational_balances' => true,
        ];

        $this->detectionServiceMock
            ->shouldReceive('getOrderSchemeMode')
            ->with($warehouseId)
            ->andReturn($mode);

        $result = $this->handler->validateOrderSchemeOperation($warehouseId, $operationType, $data);

        $this->assertFalse($result['valid']);
        $this->assertContains('Shipment operations in order scheme require reservation', $result['errors']);
        $this->assertEmpty($result['warnings']);
        $this->assertEquals($availability, $result['availability']);
    }

    public function test_validate_order_scheme_operation_shipment_without_reservation_no_strict_control(): void
    {
        $warehouseId = 'warehouse-123';
        $operationType = 'shipment';
        $data = ['date' => '2024-01-02'];
        
        $availability = [
            'operation_type' => 'shipment',
            'warehouse_id' => 'warehouse-123',
            'order_scheme_active' => true,
            'mode' => 'shipments_only',
            'control_operational_balances' => false,
            'date_checked' => '2024-01-02',
        ];

        $mode = [
            'mode' => 'shipments_only',
            'receipts_active' => false,
            'shipments_active' => true,
            'control_operational_balances' => false,
        ];

        $this->detectionServiceMock
            ->shouldReceive('getOrderSchemeMode')
            ->with($warehouseId)
            ->andReturn($mode);

        $result = $this->handler->validateOrderSchemeOperation($warehouseId, $operationType, $data);

        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['errors']);
        $this->assertContains('Shipment operations in order scheme should have reservation', $result['warnings']);
        $this->assertEquals($availability, $result['availability']);
    }

    public function test_validate_order_scheme_operation_with_valid_data(): void
    {
        $warehouseId = 'warehouse-123';
        $operationType = 'receipt';
        $data = [
            'date' => '2024-01-02',
            'document_basis_type' => 'vendor_order',
        ];
        
        $availability = [
            'operation_type' => 'receipt',
            'warehouse_id' => 'warehouse-123',
            'order_scheme_active' => true,
            'mode' => 'receipts_only',
            'control_operational_balances' => false,
            'date_checked' => '2024-01-02',
        ];

        $mode = [
            'mode' => 'receipts_only',
            'receipts_active' => true,
            'shipments_active' => false,
            'control_operational_balances' => false,
        ];

        $this->detectionServiceMock
            ->shouldReceive('getOrderSchemeMode')
            ->with($warehouseId)
            ->andReturn($mode);

        $result = $this->handler->validateOrderSchemeOperation($warehouseId, $operationType, $data);

        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['errors']);
        $this->assertEmpty($result['warnings']);
        $this->assertEquals($availability, $result['availability']);
    }

    public function test_validate_order_scheme_operation_when_order_scheme_not_active(): void
    {
        $warehouseId = 'warehouse-123';
        $operationType = 'receipt';
        $data = ['date' => '2024-01-02'];
        
        $availability = [
            'operation_type' => 'receipt',
            'warehouse_id' => 'warehouse-123',
            'order_scheme_active' => false,
            'mode' => 'regular',
            'control_operational_balances' => false,
            'date_checked' => '2024-01-02',
        ];

        $mode = [
            'mode' => 'regular',
            'receipts_active' => false,
            'shipments_active' => false,
            'control_operational_balances' => false,
        ];

        $this->detectionServiceMock
            ->shouldReceive('getOrderSchemeMode')
            ->with($warehouseId)
            ->andReturn($mode);

        $result = $this->handler->validateOrderSchemeOperation($warehouseId, $operationType, $data);

        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['errors']);
        $this->assertEmpty($result['warnings']);
        $this->assertEquals($availability, $result['availability']);
    }
}
