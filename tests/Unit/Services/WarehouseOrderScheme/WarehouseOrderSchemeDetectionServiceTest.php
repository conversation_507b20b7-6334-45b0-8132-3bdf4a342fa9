<?php

namespace Tests\Unit\Services\WarehouseOrderScheme;

use App\Entities\WarehouseOrderSchemeEntity;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseOrderSchemeDetectionService;
use Carbon\Carbon;
use Mockery;
use PHPUnit\Framework\TestCase;

class WarehouseOrderSchemeDetectionServiceTest extends TestCase
{
    private WarehouseOrderSchemeDetectionService $service;
    private $entityMock;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->entityMock = Mockery::mock(WarehouseOrderSchemeEntity::class);
        $this->service = new WarehouseOrderSchemeDetectionService($this->entityMock);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_is_order_scheme_active_for_receipts_returns_false_when_no_scheme_exists(): void
    {
        $warehouseId = 'warehouse-123';
        
        $this->entityMock
            ->shouldReceive('where')
            ->with('warehouse_id', $warehouseId)
            ->andReturnSelf();
            
        $this->entityMock
            ->shouldReceive('first')
            ->andReturn(null);

        $result = $this->service->isOrderSchemeActiveForReceipts($warehouseId);

        $this->assertFalse($result);
    }

    public function test_is_order_scheme_active_for_receipts_returns_false_when_on_coming_from_is_null(): void
    {
        $warehouseId = 'warehouse-123';
        $scheme = (object) [
            'on_coming_from' => null,
            'on_shipment_from' => '2024-01-01',
            'control_operational_balances' => true,
        ];
        
        $this->entityMock
            ->shouldReceive('where')
            ->with('warehouse_id', $warehouseId)
            ->andReturnSelf();
            
        $this->entityMock
            ->shouldReceive('first')
            ->andReturn($scheme);

        $result = $this->service->isOrderSchemeActiveForReceipts($warehouseId);

        $this->assertFalse($result);
    }

    public function test_is_order_scheme_active_for_receipts_returns_true_when_date_is_after_on_coming_from(): void
    {
        $warehouseId = 'warehouse-123';
        $scheme = (object) [
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => '2024-01-01',
            'control_operational_balances' => true,
        ];
        
        $this->entityMock
            ->shouldReceive('where')
            ->with('warehouse_id', $warehouseId)
            ->andReturnSelf();
            
        $this->entityMock
            ->shouldReceive('first')
            ->andReturn($scheme);

        Carbon::setTestNow('2024-01-02');
        $result = $this->service->isOrderSchemeActiveForReceipts($warehouseId);

        $this->assertTrue($result);
        Carbon::setTestNow();
    }

    public function test_is_order_scheme_active_for_receipts_returns_false_when_date_is_before_on_coming_from(): void
    {
        $warehouseId = 'warehouse-123';
        $scheme = (object) [
            'on_coming_from' => '2024-01-02',
            'on_shipment_from' => '2024-01-01',
            'control_operational_balances' => true,
        ];
        
        $this->entityMock
            ->shouldReceive('where')
            ->with('warehouse_id', $warehouseId)
            ->andReturnSelf();
            
        $this->entityMock
            ->shouldReceive('first')
            ->andReturn($scheme);

        Carbon::setTestNow('2024-01-01');
        $result = $this->service->isOrderSchemeActiveForReceipts($warehouseId);

        $this->assertFalse($result);
        Carbon::setTestNow();
    }

    public function test_is_order_scheme_active_for_shipments_returns_true_when_date_is_after_on_shipment_from(): void
    {
        $warehouseId = 'warehouse-123';
        $scheme = (object) [
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => '2024-01-01',
            'control_operational_balances' => true,
        ];
        
        $this->entityMock
            ->shouldReceive('where')
            ->with('warehouse_id', $warehouseId)
            ->andReturnSelf();
            
        $this->entityMock
            ->shouldReceive('first')
            ->andReturn($scheme);

        Carbon::setTestNow('2024-01-02');
        $result = $this->service->isOrderSchemeActiveForShipments($warehouseId);

        $this->assertTrue($result);
        Carbon::setTestNow();
    }

    public function test_get_order_scheme_mode_returns_regular_when_no_scheme_exists(): void
    {
        $warehouseId = 'warehouse-123';
        
        $this->entityMock
            ->shouldReceive('where')
            ->with('warehouse_id', $warehouseId)
            ->andReturnSelf();
            
        $this->entityMock
            ->shouldReceive('first')
            ->andReturn(null);

        $result = $this->service->getOrderSchemeMode($warehouseId);

        $expected = [
            'mode' => 'regular',
            'receipts_active' => false,
            'shipments_active' => false,
            'control_operational_balances' => false,
        ];

        $this->assertEquals($expected, $result);
    }

    public function test_get_order_scheme_mode_returns_full_order_scheme_when_both_dates_set(): void
    {
        $warehouseId = 'warehouse-123';
        $scheme = (object) [
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => '2024-01-01',
            'control_operational_balances' => true,
        ];
        
        $this->entityMock
            ->shouldReceive('where')
            ->with('warehouse_id', $warehouseId)
            ->andReturnSelf()
            ->times(3);
            
        $this->entityMock
            ->shouldReceive('first')
            ->andReturn($scheme)
            ->times(3);

        Carbon::setTestNow('2024-01-02');
        $result = $this->service->getOrderSchemeMode($warehouseId);

        $this->assertEquals('full_order_scheme', $result['mode']);
        $this->assertTrue($result['receipts_active']);
        $this->assertTrue($result['shipments_active']);
        $this->assertTrue($result['control_operational_balances']);
        
        Carbon::setTestNow();
    }

    public function test_get_order_scheme_mode_returns_receipts_only_when_only_on_coming_from_set(): void
    {
        $warehouseId = 'warehouse-123';
        $scheme = (object) [
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => null,
            'control_operational_balances' => false,
        ];
        
        $this->entityMock
            ->shouldReceive('where')
            ->with('warehouse_id', $warehouseId)
            ->andReturnSelf()
            ->times(3);
            
        $this->entityMock
            ->shouldReceive('first')
            ->andReturn($scheme)
            ->times(3);

        Carbon::setTestNow('2024-01-02');
        $result = $this->service->getOrderSchemeMode($warehouseId);

        $this->assertEquals('receipts_only', $result['mode']);
        $this->assertTrue($result['receipts_active']);
        $this->assertFalse($result['shipments_active']);
        
        Carbon::setTestNow();
    }

    public function test_can_enable_order_scheme_returns_false_when_scheme_already_exists(): void
    {
        $warehouseId = 'warehouse-123';
        $scheme = (object) [
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => null,
            'control_operational_balances' => false,
        ];
        
        $this->entityMock
            ->shouldReceive('where')
            ->with('warehouse_id', $warehouseId)
            ->andReturnSelf();
            
        $this->entityMock
            ->shouldReceive('first')
            ->andReturn($scheme);

        $result = $this->service->canEnableOrderScheme($warehouseId);

        $this->assertFalse($result['can_enable']);
        $this->assertContains('Order scheme is already partially or fully enabled', $result['reasons']);
    }

    public function test_can_enable_order_scheme_returns_true_when_no_scheme_exists(): void
    {
        $warehouseId = 'warehouse-123';
        
        $this->entityMock
            ->shouldReceive('where')
            ->with('warehouse_id', $warehouseId)
            ->andReturnSelf();
            
        $this->entityMock
            ->shouldReceive('first')
            ->andReturn(null);

        $result = $this->service->canEnableOrderScheme($warehouseId);

        $this->assertTrue($result['can_enable']);
        $this->assertEmpty($result['reasons']);
    }

    public function test_is_order_scheme_active_with_all_operation_type(): void
    {
        $warehouseId = 'warehouse-123';
        $scheme = (object) [
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => '2024-01-01',
            'control_operational_balances' => true,
        ];
        
        $this->entityMock
            ->shouldReceive('where')
            ->with('warehouse_id', $warehouseId)
            ->andReturnSelf()
            ->times(2);
            
        $this->entityMock
            ->shouldReceive('first')
            ->andReturn($scheme)
            ->times(2);

        Carbon::setTestNow('2024-01-02');
        $result = $this->service->isOrderSchemeActive($warehouseId, 'all');

        $this->assertTrue($result);
        Carbon::setTestNow();
    }

    public function test_is_order_scheme_active_with_any_operation_type(): void
    {
        $warehouseId = 'warehouse-123';
        $scheme = (object) [
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => null,
            'control_operational_balances' => true,
        ];
        
        $this->entityMock
            ->shouldReceive('where')
            ->with('warehouse_id', $warehouseId)
            ->andReturnSelf()
            ->times(2);
            
        $this->entityMock
            ->shouldReceive('first')
            ->andReturn($scheme)
            ->times(2);

        Carbon::setTestNow('2024-01-02');
        $result = $this->service->isOrderSchemeActive($warehouseId, 'any');

        $this->assertTrue($result);
        Carbon::setTestNow();
    }
}
