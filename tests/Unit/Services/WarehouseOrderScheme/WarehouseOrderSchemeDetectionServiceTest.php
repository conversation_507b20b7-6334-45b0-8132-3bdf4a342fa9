<?php

namespace Tests\Unit\Services\WarehouseOrderScheme;

use App\Services\Api\Internal\WarehouseOrderScheme\Handlers\SchemeDetectionHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseOrderSchemeDetectionService;
use Carbon\Carbon;
use Mockery;
use PHPUnit\Framework\TestCase;

class WarehouseOrderSchemeDetectionServiceTest extends TestCase
{
    private WarehouseOrderSchemeDetectionService $service;
    private $handlerMock;

    protected function setUp(): void
    {
        parent::setUp();

        $this->handlerMock = Mockery::mock(SchemeDetectionHandler::class);
        $this->service = new WarehouseOrderSchemeDetectionService($this->handlerMock);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_is_order_scheme_active_for_receipts_returns_false_when_no_scheme_exists(): void
    {
        $warehouseId = 'warehouse-123';

        $this->handlerMock
            ->shouldReceive('isOrderSchemeActiveForReceipts')
            ->with($warehouseId, null)
            ->andReturn(false);

        $result = $this->service->isOrderSchemeActiveForReceipts($warehouseId);

        $this->assertFalse($result);
    }

    public function test_is_order_scheme_active_for_shipments_returns_true(): void
    {
        $warehouseId = 'warehouse-123';

        $this->handlerMock
            ->shouldReceive('isOrderSchemeActiveForShipments')
            ->with($warehouseId, null)
            ->andReturn(true);

        $result = $this->service->isOrderSchemeActiveForShipments($warehouseId);

        $this->assertTrue($result);
    }

    public function test_get_order_scheme_mode_returns_expected_result(): void
    {
        $warehouseId = 'warehouse-123';
        $expected = [
            'mode' => 'full_order_scheme',
            'receipts_active' => true,
            'shipments_active' => true,
            'control_operational_balances' => true,
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => '2024-01-01',
        ];

        $this->handlerMock
            ->shouldReceive('getOrderSchemeMode')
            ->with($warehouseId)
            ->andReturn($expected);

        $result = $this->service->getOrderSchemeMode($warehouseId);

        $this->assertEquals($expected, $result);
    }

    public function test_is_order_scheme_active_delegates_to_handler(): void
    {
        $warehouseId = 'warehouse-123';
        $operationType = 'all';
        $date = '2024-01-02';

        $this->handlerMock
            ->shouldReceive('isOrderSchemeActive')
            ->with($warehouseId, $operationType, $date)
            ->andReturn(true);

        $result = $this->service->isOrderSchemeActive($warehouseId, $operationType, $date);

        $this->assertTrue($result);
    }

    public function test_get_warehouse_scheme_delegates_to_handler(): void
    {
        $warehouseId = 'warehouse-123';
        $expectedScheme = (object) [
            'warehouse_id' => $warehouseId,
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => '2024-01-01',
            'control_operational_balances' => true,
        ];

        $this->handlerMock
            ->shouldReceive('getWarehouseScheme')
            ->with($warehouseId)
            ->andReturn($expectedScheme);

        $result = $this->service->getWarehouseScheme($warehouseId);

        $this->assertEquals($expectedScheme, $result);
    }
}
