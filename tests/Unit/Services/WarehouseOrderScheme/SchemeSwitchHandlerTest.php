<?php

namespace Tests\Unit\Services\WarehouseOrderScheme;

use App\Contracts\Repositories\WarehouseOrderSchemesRepositoryContract;
use App\Services\Api\Internal\WarehouseOrderScheme\Handlers\SchemeSwitchHandler;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseOrderSchemeDetectionService;
use Carbon\Carbon;
use InvalidArgumentException;
use Mockery;
use PHPUnit\Framework\TestCase;

class SchemeSwitchHandlerTest extends TestCase
{
    private SchemeSwitchHandler $handler;
    private $detectionServiceMock;
    private $repositoryMock;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->detectionServiceMock = Mockery::mock(WarehouseOrderSchemeDetectionService::class);
        $this->repositoryMock = Mockery::mock(WarehouseOrderSchemesRepositoryContract::class);
        $this->handler = new SchemeSwitchHandler($this->detectionServiceMock, $this->repositoryMock);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_enable_order_scheme_throws_exception_when_cannot_enable(): void
    {
        $warehouseId = 'warehouse-123';
        $settings = [
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => '2024-01-01',
            'control_operational_balances' => true,
        ];

        $this->detectionServiceMock
            ->shouldReceive('canEnableOrderScheme')
            ->with($warehouseId)
            ->andReturn([
                'can_enable' => false,
                'reasons' => ['Order scheme is already partially or fully enabled'],
            ]);

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Cannot enable order scheme: Order scheme is already partially or fully enabled');

        $this->handler->enableOrderScheme($warehouseId, $settings);
    }

    public function test_enable_order_scheme_creates_new_scheme_when_none_exists(): void
    {
        $warehouseId = 'warehouse-123';
        $settings = [
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => '2024-01-01',
            'control_operational_balances' => true,
        ];

        $this->detectionServiceMock
            ->shouldReceive('canEnableOrderScheme')
            ->with($warehouseId)
            ->andReturn(['can_enable' => true, 'reasons' => []]);

        $this->detectionServiceMock
            ->shouldReceive('getWarehouseScheme')
            ->with($warehouseId)
            ->andReturn(null);

        $expectedData = [
            'warehouse_id' => $warehouseId,
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => '2024-01-01',
            'control_operational_balances' => true,
        ];

        $this->repositoryMock
            ->shouldReceive('insert')
            ->with(Mockery::on(function ($data) use ($expectedData) {
                return $data['warehouse_id'] === $expectedData['warehouse_id'] &&
                       $data['on_coming_from'] === $expectedData['on_coming_from'] &&
                       $data['on_shipment_from'] === $expectedData['on_shipment_from'] &&
                       $data['control_operational_balances'] === $expectedData['control_operational_balances'] &&
                       isset($data['created_at']);
            }));

        $expectedMode = [
            'mode' => 'full_order_scheme',
            'receipts_active' => true,
            'shipments_active' => true,
            'control_operational_balances' => true,
        ];

        $this->detectionServiceMock
            ->shouldReceive('getOrderSchemeMode')
            ->with($warehouseId)
            ->andReturn($expectedMode);

        $result = $this->handler->enableOrderScheme($warehouseId, $settings);

        $this->assertTrue($result['success']);
        $this->assertEquals($warehouseId, $result['warehouse_id']);
        $this->assertEquals($expectedData, $result['settings']);
        $this->assertEquals($expectedMode, $result['mode']);
    }

    public function test_enable_order_scheme_updates_existing_scheme(): void
    {
        $warehouseId = 'warehouse-123';
        $settings = [
            'on_coming_from' => '2024-01-01',
            'control_operational_balances' => false,
        ];

        $existingScheme = (object) [
            'warehouse_id' => $warehouseId,
            'on_coming_from' => null,
            'on_shipment_from' => null,
            'control_operational_balances' => true,
        ];

        $this->detectionServiceMock
            ->shouldReceive('canEnableOrderScheme')
            ->with($warehouseId)
            ->andReturn(['can_enable' => true, 'reasons' => []]);

        $this->detectionServiceMock
            ->shouldReceive('getWarehouseScheme')
            ->with($warehouseId)
            ->andReturn($existingScheme);

        $expectedData = [
            'warehouse_id' => $warehouseId,
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => null,
            'control_operational_balances' => false,
        ];

        $this->repositoryMock
            ->shouldReceive('updateByWarehouseId')
            ->with($warehouseId, $expectedData);

        $expectedMode = [
            'mode' => 'receipts_only',
            'receipts_active' => true,
            'shipments_active' => false,
            'control_operational_balances' => false,
        ];

        $this->detectionServiceMock
            ->shouldReceive('getOrderSchemeMode')
            ->with($warehouseId)
            ->andReturn($expectedMode);

        $result = $this->handler->enableOrderScheme($warehouseId, $settings);

        $this->assertTrue($result['success']);
        $this->assertEquals($warehouseId, $result['warehouse_id']);
        $this->assertEquals($expectedData, $result['settings']);
        $this->assertEquals($expectedMode, $result['mode']);
    }

    public function test_enable_order_scheme_throws_exception_for_invalid_date_format(): void
    {
        $warehouseId = 'warehouse-123';
        $settings = [
            'on_coming_from' => 'invalid-date',
        ];

        $this->detectionServiceMock
            ->shouldReceive('canEnableOrderScheme')
            ->with($warehouseId)
            ->andReturn(['can_enable' => true, 'reasons' => []]);

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid on_coming_from date format');

        $this->handler->enableOrderScheme($warehouseId, $settings);
    }

    public function test_update_order_scheme_settings_throws_exception_when_scheme_not_found(): void
    {
        $warehouseId = 'warehouse-123';
        $settings = ['control_operational_balances' => false];

        $this->detectionServiceMock
            ->shouldReceive('getWarehouseScheme')
            ->with($warehouseId)
            ->andReturn(null);

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Order scheme not found for warehouse');

        $this->handler->updateOrderSchemeSettings($warehouseId, $settings);
    }

    public function test_update_order_scheme_settings_updates_existing_scheme(): void
    {
        $warehouseId = 'warehouse-123';
        $settings = ['control_operational_balances' => false];

        $existingScheme = (object) [
            'warehouse_id' => $warehouseId,
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => '2024-01-01',
            'control_operational_balances' => true,
        ];

        $this->detectionServiceMock
            ->shouldReceive('getWarehouseScheme')
            ->with($warehouseId)
            ->andReturn($existingScheme);

        $this->repositoryMock
            ->shouldReceive('updateByWarehouseId')
            ->with($warehouseId, $settings);

        $expectedMode = [
            'mode' => 'full_order_scheme',
            'receipts_active' => true,
            'shipments_active' => true,
            'control_operational_balances' => false,
        ];

        $this->detectionServiceMock
            ->shouldReceive('getOrderSchemeMode')
            ->with($warehouseId)
            ->andReturn($expectedMode);

        $result = $this->handler->updateOrderSchemeSettings($warehouseId, $settings);

        $this->assertTrue($result['success']);
        $this->assertEquals($warehouseId, $result['warehouse_id']);
        $this->assertEquals($settings, $result['updated_settings']);
        $this->assertEquals($expectedMode, $result['mode']);
    }

    public function test_disable_order_scheme_disables_all_operations(): void
    {
        $warehouseId = 'warehouse-123';

        $existingScheme = (object) [
            'warehouse_id' => $warehouseId,
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => '2024-01-01',
            'control_operational_balances' => true,
        ];

        $this->detectionServiceMock
            ->shouldReceive('getWarehouseScheme')
            ->with($warehouseId)
            ->andReturn($existingScheme);

        $expectedUpdateData = [
            'on_coming_from' => null,
            'on_shipment_from' => null,
        ];

        $this->repositoryMock
            ->shouldReceive('updateByWarehouseId')
            ->with($warehouseId, $expectedUpdateData);

        $expectedMode = [
            'mode' => 'regular',
            'receipts_active' => false,
            'shipments_active' => false,
            'control_operational_balances' => true,
        ];

        $this->detectionServiceMock
            ->shouldReceive('getOrderSchemeMode')
            ->with($warehouseId)
            ->andReturn($expectedMode);

        $result = $this->handler->disableOrderScheme($warehouseId, 'all');

        $this->assertTrue($result['success']);
        $this->assertEquals($warehouseId, $result['warehouse_id']);
        $this->assertEquals('all', $result['disabled_operations']);
        $this->assertEquals($expectedMode, $result['mode']);
    }

    public function test_disable_order_scheme_disables_only_receipts(): void
    {
        $warehouseId = 'warehouse-123';

        $existingScheme = (object) [
            'warehouse_id' => $warehouseId,
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => '2024-01-01',
            'control_operational_balances' => true,
        ];

        $this->detectionServiceMock
            ->shouldReceive('getWarehouseScheme')
            ->with($warehouseId)
            ->andReturn($existingScheme);

        $expectedUpdateData = [
            'on_coming_from' => null,
        ];

        $this->repositoryMock
            ->shouldReceive('updateByWarehouseId')
            ->with($warehouseId, $expectedUpdateData);

        $expectedMode = [
            'mode' => 'shipments_only',
            'receipts_active' => false,
            'shipments_active' => true,
            'control_operational_balances' => true,
        ];

        $this->detectionServiceMock
            ->shouldReceive('getOrderSchemeMode')
            ->with($warehouseId)
            ->andReturn($expectedMode);

        $result = $this->handler->disableOrderScheme($warehouseId, 'receipts');

        $this->assertTrue($result['success']);
        $this->assertEquals($warehouseId, $result['warehouse_id']);
        $this->assertEquals('receipts', $result['disabled_operations']);
        $this->assertEquals($expectedMode, $result['mode']);
    }

    public function test_disable_order_scheme_throws_exception_for_invalid_operation_type(): void
    {
        $warehouseId = 'warehouse-123';

        $existingScheme = (object) [
            'warehouse_id' => $warehouseId,
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => '2024-01-01',
            'control_operational_balances' => true,
        ];

        $this->detectionServiceMock
            ->shouldReceive('getWarehouseScheme')
            ->with($warehouseId)
            ->andReturn($existingScheme);

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid operation type');

        $this->handler->disableOrderScheme($warehouseId, 'invalid');
    }
}
