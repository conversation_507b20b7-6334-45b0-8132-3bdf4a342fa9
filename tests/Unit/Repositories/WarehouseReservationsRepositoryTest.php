<?php

namespace Tests\Unit\Repositories;

use App\Contracts\Repositories\WarehouseReservationsRepositoryContract;
use App\Models\Cabinet;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Product;
use App\Models\Status;
use App\Models\Warehouse;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class WarehouseReservationsRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private WarehouseReservationsRepositoryContract $repository;
    private Warehouse $warehouse;
    private Product $product;
    private Employee $employee;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = app(WarehouseReservationsRepositoryContract::class);
        
        $cabinet = Cabinet::factory()->create();
        $department = Department::factory()->create();
        $this->employee = Employee::factory()->create();
        $status = Status::factory()->create();
        
        $this->warehouse = Warehouse::factory()->create([
            'cabinet_id' => $cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $department->id,
        ]);
        
        $this->product = Product::factory()->create();
    }

    public function test_can_insert_reservation(): void
    {
        $data = [
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'reserved_quantity' => 50,
            'used_quantity' => 0,
            'document_type' => 'order',
            'document_id' => '550e8400-e29b-41d4-a716-446655440000',
            'reservation_type' => 'order',
            'priority' => 5,
            'reserved_at' => Carbon::now(),
            'status' => 'active',
            'created_by' => $this->employee->id,
        ];

        $result = $this->repository->insert($data);

        $this->assertTrue($result);
    }

    public function test_can_find_reservation_by_id(): void
    {
        $data = [
            'id' => '550e8400-e29b-41d4-a716-446655440010',
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'reserved_quantity' => 50,
            'used_quantity' => 0,
            'document_type' => 'order',
            'document_id' => '550e8400-e29b-41d4-a716-446655440000',
            'reservation_type' => 'order',
            'priority' => 5,
            'reserved_at' => Carbon::now(),
            'status' => 'active',
            'created_by' => $this->employee->id,
        ];

        $this->repository->insert($data);
        $result = $this->repository->findById($data['id']);

        $this->assertNotNull($result);
        $this->assertEquals($data['id'], $result->id);
        $this->assertEquals($data['quantity'], $result->quantity);
    }

    public function test_can_get_reservations_by_document(): void
    {
        $documentId = '550e8400-e29b-41d4-a716-446655440000';
        
        $data1 = [
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'reserved_quantity' => 50,
            'used_quantity' => 0,
            'document_type' => 'order',
            'document_id' => $documentId,
            'reservation_type' => 'order',
            'priority' => 5,
            'reserved_at' => Carbon::now(),
            'status' => 'active',
            'created_by' => $this->employee->id,
        ];

        $data2 = [
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 50,
            'reserved_quantity' => 25,
            'used_quantity' => 0,
            'document_type' => 'order',
            'document_id' => $documentId,
            'reservation_type' => 'order',
            'priority' => 3,
            'reserved_at' => Carbon::now(),
            'status' => 'active',
            'created_by' => $this->employee->id,
        ];

        $this->repository->insert($data1);
        $this->repository->insert($data2);

        $result = $this->repository->getByDocument('order', $documentId);

        $this->assertCount(2, $result);
        // Проверяем сортировку по приоритету
        $this->assertEquals(3, $result->first()->priority);
    }

    public function test_can_get_active_reservations(): void
    {
        // Активный резерв
        $activeData = [
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'reserved_quantity' => 50,
            'used_quantity' => 0,
            'document_type' => 'order',
            'document_id' => '550e8400-e29b-41d4-a716-446655440000',
            'reservation_type' => 'order',
            'priority' => 5,
            'reserved_at' => Carbon::now(),
            'expires_at' => Carbon::now()->addDay(),
            'status' => 'active',
            'created_by' => $this->employee->id,
        ];

        // Просроченный резерв
        $expiredData = [
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 50,
            'reserved_quantity' => 25,
            'used_quantity' => 0,
            'document_type' => 'order',
            'document_id' => '550e8400-e29b-41d4-a716-446655440001',
            'reservation_type' => 'order',
            'priority' => 5,
            'reserved_at' => Carbon::now()->subDay(),
            'expires_at' => Carbon::now()->subHour(),
            'status' => 'active',
            'created_by' => $this->employee->id,
        ];

        $this->repository->insert($activeData);
        $this->repository->insert($expiredData);

        $result = $this->repository->getActiveReservations($this->warehouse->id, $this->product->id);

        $this->assertCount(1, $result);
    }

    public function test_can_get_expired_reservations(): void
    {
        $expiredData = [
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 50,
            'reserved_quantity' => 25,
            'used_quantity' => 0,
            'document_type' => 'order',
            'document_id' => '550e8400-e29b-41d4-a716-446655440001',
            'reservation_type' => 'order',
            'priority' => 5,
            'reserved_at' => Carbon::now()->subDay(),
            'expires_at' => Carbon::now()->subHour(),
            'status' => 'active',
            'created_by' => $this->employee->id,
        ];

        $this->repository->insert($expiredData);

        $result = $this->repository->getExpiredReservations();

        $this->assertCount(1, $result);
    }

    public function test_can_calculate_available_quantity(): void
    {
        $data1 = [
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'reserved_quantity' => 80,
            'used_quantity' => 20,
            'document_type' => 'order',
            'document_id' => '550e8400-e29b-41d4-a716-446655440000',
            'reservation_type' => 'order',
            'priority' => 5,
            'reserved_at' => Carbon::now(),
            'status' => 'active',
            'created_by' => $this->employee->id,
        ];

        $data2 = [
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 50,
            'reserved_quantity' => 30,
            'used_quantity' => 10,
            'document_type' => 'order',
            'document_id' => '550e8400-e29b-41d4-a716-446655440001',
            'reservation_type' => 'order',
            'priority' => 5,
            'reserved_at' => Carbon::now(),
            'status' => 'active',
            'created_by' => $this->employee->id,
        ];

        $this->repository->insert($data1);
        $this->repository->insert($data2);

        $result = $this->repository->getAvailableQuantity($this->warehouse->id, $this->product->id);

        // (80-20) + (30-10) = 60 + 20 = 80
        $this->assertEquals(80, $result);
    }

    public function test_can_find_best_reservation_for_use(): void
    {
        // Резерв с высоким приоритетом (низкое число = высокий приоритет)
        $highPriorityData = [
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'reserved_quantity' => 80,
            'used_quantity' => 0,
            'document_type' => 'order',
            'document_id' => '550e8400-e29b-41d4-a716-446655440000',
            'reservation_type' => 'order',
            'priority' => 1,
            'reserved_at' => Carbon::now(),
            'status' => 'active',
            'created_by' => $this->employee->id,
        ];

        // Резерв с низким приоритетом
        $lowPriorityData = [
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 50,
            'reserved_quantity' => 40,
            'used_quantity' => 0,
            'document_type' => 'order',
            'document_id' => '550e8400-e29b-41d4-a716-446655440001',
            'reservation_type' => 'order',
            'priority' => 5,
            'reserved_at' => Carbon::now()->subHour(),
            'status' => 'active',
            'created_by' => $this->employee->id,
        ];

        $this->repository->insert($lowPriorityData);
        $this->repository->insert($highPriorityData);

        $result = $this->repository->findBestReservationForUse(
            $this->warehouse->id, 
            $this->product->id, 
            50
        );

        $this->assertNotNull($result);
        $this->assertEquals(1, $result->priority); // Должен выбрать резерв с высоким приоритетом
    }
}
