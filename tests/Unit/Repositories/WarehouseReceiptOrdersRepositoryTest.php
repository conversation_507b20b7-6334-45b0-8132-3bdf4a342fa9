<?php

namespace Tests\Unit\Repositories;

use App\Contracts\Repositories\WarehouseReceiptOrdersRepositoryContract;
use App\Models\Cabinet;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Status;
use App\Models\Warehouse;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class WarehouseReceiptOrdersRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private WarehouseReceiptOrdersRepositoryContract $repository;
    private Cabinet $cabinet;
    private Employee $employee;
    private Department $department;
    private Warehouse $warehouse;
    private Status $status;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = app(WarehouseReceiptOrdersRepositoryContract::class);

        $this->cabinet = Cabinet::factory()->create();
        $this->employee = Employee::factory()->create();
        $this->department = Department::factory()->create();
        $this->warehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ]);
        $this->status = Status::factory()->create();
    }

    public function test_can_insert_receipt_order(): void
    {
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'number' => 'RO-001',
            'date_from' => '2024-01-01 10:00:00',
            'status_id' => $this->status->id,
            'held' => false,
            'reason' => 'Test receipt',
            'total_quantity' => 10,
        ];

        $result = $this->repository->insert($data);

        $this->assertTrue($result);
    }

    public function test_can_find_receipt_order_by_id(): void
    {
        $data = [
            'id' => '550e8400-e29b-41d4-a716-446655440010',
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'number' => 'RO-002',
            'date_from' => '2024-01-01 10:00:00',
            'status_id' => $this->status->id,
            'held' => false,
            'reason' => 'Test receipt',
            'total_quantity' => 10,
        ];

        $this->repository->insert($data);
        $result = $this->repository->findById($data['id']);

        $this->assertNotNull($result);
        $this->assertEquals($data['id'], $result->id);
        $this->assertEquals($data['number'], $result->number);
    }

    public function test_can_get_by_warehouse(): void
    {
        $data1 = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'number' => 'RO-003',
            'date_from' => '2024-01-01 10:00:00',
            'status_id' => $this->status->id,
            'held' => false,
            'total_quantity' => 10,
        ];

        $data2 = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'number' => 'RO-004',
            'date_from' => '2024-01-02 10:00:00',
            'status_id' => $this->status->id,
            'held' => true,
            'total_quantity' => 20,
        ];

        $this->repository->insert($data1);
        $this->repository->insert($data2);

        $result = $this->repository->getByWarehouse($this->warehouse->id);

        $this->assertCount(2, $result);
    }

    public function test_can_filter_by_held_status(): void
    {
        $data1 = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'number' => 'RO-005',
            'date_from' => '2024-01-01 10:00:00',
            'status_id' => $this->status->id,
            'held' => false,
            'total_quantity' => 10,
        ];

        $data2 = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'number' => 'RO-006',
            'date_from' => '2024-01-02 10:00:00',
            'status_id' => $this->status->id,
            'held' => true,
            'total_quantity' => 20,
        ];

        $this->repository->insert($data1);
        $this->repository->insert($data2);

        $heldOrders = $this->repository->getByWarehouse($this->warehouse->id, ['held' => true]);
        $unheldOrders = $this->repository->getByWarehouse($this->warehouse->id, ['held' => false]);

        $this->assertCount(1, $heldOrders);
        $this->assertCount(1, $unheldOrders);
        $this->assertTrue($heldOrders->first()->held);
        $this->assertFalse($unheldOrders->first()->held);
    }
}
