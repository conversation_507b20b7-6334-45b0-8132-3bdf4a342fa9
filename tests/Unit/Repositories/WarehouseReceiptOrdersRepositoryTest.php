<?php

namespace Tests\Unit\Repositories;

use App\Contracts\Repositories\WarehouseReceiptOrdersRepositoryContract;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class WarehouseReceiptOrdersRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private WarehouseReceiptOrdersRepositoryContract $repository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = app(WarehouseReceiptOrdersRepositoryContract::class);
    }

    public function test_repository_is_registered(): void
    {
        $this->assertInstanceOf(
            WarehouseReceiptOrdersRepositoryContract::class,
            $this->repository
        );
    }

    public function test_can_find_receipt_order_by_id(): void
    {
        $data = [
            'id' => '550e8400-e29b-41d4-a716-446655440010',
            'cabinet_id' => '550e8400-e29b-41d4-a716-446655440000',
            'employee_id' => '550e8400-e29b-41d4-a716-446655440001',
            'department_id' => '550e8400-e29b-41d4-a716-446655440002',
            'warehouse_id' => '550e8400-e29b-41d4-a716-446655440003',
            'number' => 'RO-002',
            'date_from' => '2024-01-01 10:00:00',
            'status_id' => '550e8400-e29b-41d4-a716-446655440004',
            'held' => false,
            'reason' => 'Test receipt',
            'total_quantity' => 10,
        ];

        $this->repository->insert($data);
        $result = $this->repository->findById($data['id']);

        $this->assertNotNull($result);
        $this->assertEquals($data['id'], $result->id);
        $this->assertEquals($data['number'], $result->number);
    }

    public function test_can_get_by_warehouse(): void
    {
        $warehouseId = '550e8400-e29b-41d4-a716-446655440003';
        
        $data1 = [
            'cabinet_id' => '550e8400-e29b-41d4-a716-446655440000',
            'employee_id' => '550e8400-e29b-41d4-a716-446655440001',
            'department_id' => '550e8400-e29b-41d4-a716-446655440002',
            'warehouse_id' => $warehouseId,
            'number' => 'RO-003',
            'date_from' => '2024-01-01 10:00:00',
            'status_id' => '550e8400-e29b-41d4-a716-446655440004',
            'held' => false,
            'total_quantity' => 10,
        ];

        $data2 = [
            'cabinet_id' => '550e8400-e29b-41d4-a716-446655440000',
            'employee_id' => '550e8400-e29b-41d4-a716-446655440001',
            'department_id' => '550e8400-e29b-41d4-a716-446655440002',
            'warehouse_id' => $warehouseId,
            'number' => 'RO-004',
            'date_from' => '2024-01-02 10:00:00',
            'status_id' => '550e8400-e29b-41d4-a716-446655440004',
            'held' => true,
            'total_quantity' => 20,
        ];

        $this->repository->insert($data1);
        $this->repository->insert($data2);

        $result = $this->repository->getByWarehouse($warehouseId);

        $this->assertCount(2, $result);
    }

    public function test_can_filter_by_held_status(): void
    {
        $warehouseId = '550e8400-e29b-41d4-a716-446655440003';
        
        $data1 = [
            'cabinet_id' => '550e8400-e29b-41d4-a716-446655440000',
            'employee_id' => '550e8400-e29b-41d4-a716-446655440001',
            'department_id' => '550e8400-e29b-41d4-a716-446655440002',
            'warehouse_id' => $warehouseId,
            'number' => 'RO-005',
            'date_from' => '2024-01-01 10:00:00',
            'status_id' => '550e8400-e29b-41d4-a716-446655440004',
            'held' => false,
            'total_quantity' => 10,
        ];

        $data2 = [
            'cabinet_id' => '550e8400-e29b-41d4-a716-446655440000',
            'employee_id' => '550e8400-e29b-41d4-a716-446655440001',
            'department_id' => '550e8400-e29b-41d4-a716-446655440002',
            'warehouse_id' => $warehouseId,
            'number' => 'RO-006',
            'date_from' => '2024-01-02 10:00:00',
            'status_id' => '550e8400-e29b-41d4-a716-446655440004',
            'held' => true,
            'total_quantity' => 20,
        ];

        $this->repository->insert($data1);
        $this->repository->insert($data2);

        $heldOrders = $this->repository->getByWarehouse($warehouseId, ['held' => true]);
        $unheldOrders = $this->repository->getByWarehouse($warehouseId, ['held' => false]);

        $this->assertCount(1, $heldOrders);
        $this->assertCount(1, $unheldOrders);
        $this->assertTrue($heldOrders->first()->held);
        $this->assertFalse($unheldOrders->first()->held);
    }
}
