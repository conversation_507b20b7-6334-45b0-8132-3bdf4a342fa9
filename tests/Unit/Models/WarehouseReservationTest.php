<?php

namespace Tests\Unit\Models;

use App\Models\Cabinet;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Product;
use App\Models\Status;
use App\Models\Warehouse;
use App\Models\WarehouseReservation;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class WarehouseReservationTest extends TestCase
{
    use RefreshDatabase;

    private Warehouse $warehouse;
    private Product $product;
    private Employee $employee;

    protected function setUp(): void
    {
        parent::setUp();

        $cabinet = Cabinet::factory()->create();
        $department = Department::factory()->create();
        $this->employee = Employee::factory()->create();
        $status = Status::factory()->create();

        $this->warehouse = Warehouse::factory()->create([
            'cabinet_id' => $cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $department->id,
        ]);

        $this->product = Product::factory()->create();
    }

    public function test_can_create_reservation(): void
    {
        $reservation = WarehouseReservation::create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'reserved_quantity' => 50,
            'used_quantity' => 0,
            'document_type' => 'order',
            'document_id' => '550e8400-e29b-41d4-a716-446655440002',
            'reservation_type' => 'order',
            'priority' => 5,
            'reserved_at' => Carbon::now(),
            'status' => 'active',
            'created_by' => $this->employee->id,
        ]);

        $this->assertDatabaseHas('warehouse_reservations', [
            'id' => $reservation->id,
            'quantity' => 100,
            'reserved_quantity' => 50,
            'status' => 'active'
        ]);
    }

    public function test_available_quantity_calculation(): void
    {
        $reservation = new WarehouseReservation([
            'reserved_quantity' => 100,
            'used_quantity' => 30
        ]);

        $this->assertEquals(70, $reservation->getAvailableQuantityAttribute());
    }

    public function test_remaining_quantity_calculation(): void
    {
        $reservation = new WarehouseReservation([
            'quantity' => 100,
            'reserved_quantity' => 60
        ]);

        $this->assertEquals(40, $reservation->getRemainingQuantityAttribute());
    }

    public function test_is_active_when_status_active_and_not_expired(): void
    {
        $reservation = new WarehouseReservation([
            'status' => 'active',
            'expires_at' => Carbon::now()->addDay()
        ]);

        $this->assertTrue($reservation->isActive());
    }

    public function test_is_not_active_when_expired(): void
    {
        $reservation = new WarehouseReservation([
            'status' => 'active',
            'expires_at' => Carbon::now()->subDay()
        ]);

        $this->assertFalse($reservation->isActive());
    }

    public function test_is_expired_when_expires_at_is_past(): void
    {
        $reservation = new WarehouseReservation([
            'expires_at' => Carbon::now()->subHour()
        ]);

        $this->assertTrue($reservation->isExpired());
    }

    public function test_is_partial_when_partially_reserved(): void
    {
        $reservation = new WarehouseReservation([
            'quantity' => 100,
            'reserved_quantity' => 50
        ]);

        $this->assertTrue($reservation->isPartial());
    }

    public function test_is_fulfilled_when_fully_reserved(): void
    {
        $reservation = new WarehouseReservation([
            'quantity' => 100,
            'reserved_quantity' => 100
        ]);

        $this->assertTrue($reservation->isFulfilled());
    }

    public function test_can_be_used_when_sufficient_available_quantity(): void
    {
        $reservation = new WarehouseReservation([
            'status' => 'active',
            'reserved_quantity' => 100,
            'used_quantity' => 20,
            'expires_at' => Carbon::now()->addDay()
        ]);

        $this->assertTrue($reservation->canBeUsed(50));
        $this->assertFalse($reservation->canBeUsed(90));
    }

    public function test_reserve_method_increases_reserved_quantity(): void
    {
        $reservation = WarehouseReservation::create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'reserved_quantity' => 50,
            'used_quantity' => 0,
            'document_type' => 'order',
            'document_id' => '550e8400-e29b-41d4-a716-446655440002',
            'reservation_type' => 'order',
            'priority' => 5,
            'reserved_at' => Carbon::now(),
            'status' => 'active',
            'created_by' => $this->employee->id,
        ]);

        $result = $reservation->reserve(30);

        $this->assertTrue($result);
        $this->assertEquals(80, $reservation->fresh()->reserved_quantity);
    }

    public function test_reserve_method_fails_when_insufficient_quantity(): void
    {
        $reservation = WarehouseReservation::create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'reserved_quantity' => 90,
            'used_quantity' => 0,
            'document_type' => 'order',
            'document_id' => '550e8400-e29b-41d4-a716-446655440002',
            'reservation_type' => 'order',
            'priority' => 5,
            'reserved_at' => Carbon::now(),
            'status' => 'active',
            'created_by' => $this->employee->id,
        ]);

        $result = $reservation->reserve(20);

        $this->assertFalse($result);
        $this->assertEquals(90, $reservation->fresh()->reserved_quantity);
    }

    public function test_use_method_increases_used_quantity(): void
    {
        $reservation = WarehouseReservation::create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'reserved_quantity' => 80,
            'used_quantity' => 20,
            'document_type' => 'order',
            'document_id' => '550e8400-e29b-41d4-a716-446655440002',
            'reservation_type' => 'order',
            'priority' => 5,
            'reserved_at' => Carbon::now(),
            'status' => 'active',
            'created_by' => $this->employee->id,
        ]);

        $result = $reservation->use(30);

        $this->assertTrue($result);
        $this->assertEquals(50, $reservation->fresh()->used_quantity);
    }

    public function test_cancel_method_sets_cancelled_status(): void
    {
        $anotherEmployee = Employee::factory()->create();

        $reservation = WarehouseReservation::create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'reserved_quantity' => 50,
            'used_quantity' => 0,
            'document_type' => 'order',
            'document_id' => '550e8400-e29b-41d4-a716-446655440002',
            'reservation_type' => 'order',
            'priority' => 5,
            'reserved_at' => Carbon::now(),
            'status' => 'active',
            'created_by' => $this->employee->id,
        ]);

        $result = $reservation->cancel('Test cancellation', $anotherEmployee->id);

        $this->assertTrue($result);

        $fresh = $reservation->fresh();
        $this->assertEquals('cancelled', $fresh->status);
        $this->assertEquals('Test cancellation', $fresh->cancellation_reason);
        $this->assertEquals($anotherEmployee->id, $fresh->cancelled_by);
        $this->assertNotNull($fresh->cancelled_at);
    }
}
