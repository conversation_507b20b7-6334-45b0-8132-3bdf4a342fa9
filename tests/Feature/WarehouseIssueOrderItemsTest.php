<?php

namespace Tests\Feature;

use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Product;
use App\Models\User;
use App\Models\VatRate;
use App\Models\Warehouse;
use App\Models\WarehouseIssueOrder;
use App\Models\WarehouseIssueOrderItem;
use App\Models\WarehouseItem;
use App\Models\WarehouseOrderScheme;
use App\Models\WarehouseReservation;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class WarehouseIssueOrderItemsTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;

    protected $employee;
    protected $cabinet;
    protected $department;
    protected $warehouse;
    protected $product;
    protected $vatRate;
    protected $warehouseItem;
    protected $issueOrder;

    protected function setUp(): void
    {
        parent::setUp();
        $this->refreshApplication();

        Queue::fake();

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);
        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->warehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Создаем ордерную схему отгрузок
        WarehouseOrderScheme::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'on_shipment_from' => '2024-01-01',
        ]);

        $this->product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->vatRate = VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->warehouseItem = WarehouseItem::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'available_quantity' => 100,
            'quality_status' => 'good',
            'batch_number' => 'BATCH001',
        ]);

        $this->issueOrder = WarehouseIssueOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'held' => false,
        ]);
    }

    public function test_creates_issue_order_items_with_order(): void
    {
        // Arrange
        $issueOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'number' => 'РО-001',
            'date_from' => '2024-01-15',
            'write_off_reason' => 'defective',
            'reason_description' => 'Обнаружен брак при проверке',
            'total_quantity' => 30,
            'total_cost' => '4500.00'
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/issue-orders', $issueOrderData);

        // Assert
        $response->assertStatus(201);

        $issueOrder = WarehouseIssueOrder::where('number', 'РО-001')->first();
        $this->assertNotNull($issueOrder);

        // Проверяем основные поля ордера
        $this->assertEquals($this->cabinet->id, $issueOrder->cabinet_id);
        $this->assertEquals($this->warehouse->id, $issueOrder->warehouse_id);
        $this->assertEquals('defective', $issueOrder->write_off_reason);
        $this->assertEquals('Обнаружен брак при проверке', $issueOrder->reason_description);
        $this->assertEquals(30, $issueOrder->total_quantity);
        $this->assertEquals('4500.00', $issueOrder->total_cost);
    }

    public function test_creates_issue_order_item_separately(): void
    {
        // Arrange - создаем расходный ордер без позиций
        $issueOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'number' => 'РО-002',
            'date_from' => '2024-01-15',
            'write_off_reason' => 'defective',
            'reason_description' => 'Обнаружен брак',
            'total_quantity' => 0,
            'total_cost' => '0.00'
        ];

        $orderResponse = $this->postJson('/api/internal/warehouses/issue-orders', $issueOrderData);
        $orderResponse->assertStatus(201);

        $issueOrder = WarehouseIssueOrder::where('number', 'РО-002')->first();
        $this->assertNotNull($issueOrder);

        // Arrange - данные для позиции
        $itemData = [
            'warehouse_issue_order_id' => $issueOrder->id,
            'warehouse_item_id' => $this->warehouseItem->id,
            'quantity' => 20,
            'unit_price' => '150.00',
            'total_price' => '3000.00',
            'comment' => 'Списание брака'
        ];

        // Act - создаем позицию
        $response = $this->postJson('/api/internal/warehouses/issue-order-items', $itemData);

        // Assert
        $response->assertStatus(201);
        $response->assertJsonStructure([
            'id',
            'message'
        ]);

        // Проверяем, что позиция создалась
        $this->assertDatabaseHas('warehouse_issue_order_items', [
            'warehouse_issue_order_id' => $issueOrder->id,
            'warehouse_item_id' => $this->warehouseItem->id,
            'quantity' => 20,
            'unit_price' => '150.00',
            'total_price' => '3000.00'
        ]);

        // Проверяем, что обновились итоги ордера
        $this->assertDatabaseHas('warehouse_issue_orders', [
            'id' => $issueOrder->id,
            'total_quantity' => 20,
            'total_cost' => '3000.00'
        ]);
    }

    public function test_validates_warehouse_item_availability(): void
    {
        // Arrange - создаем ордер
        $issueOrder = WarehouseIssueOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'held' => false
        ]);

        // Пытаемся списать больше, чем есть в наличии
        $itemData = [
            'warehouse_issue_order_id' => $issueOrder->id,
            'warehouse_item_id' => $this->warehouseItem->id,
            'quantity' => 1000, // больше чем available_quantity
            'unit_price' => '150.00',
            'total_price' => '150000.00'
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/issue-order/items', $itemData);

        // Assert
        $response->assertStatus(422);
        $response->assertJsonFragment(['message' => 'Недостаточно товара в наличии']);
    }

    public function test_gets_issue_order_items_list(): void
    {
        // Arrange - создаем ордер и позицию
        $issueOrder = WarehouseIssueOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ]);

        $item = WarehouseIssueOrderItem::factory()->create([
            'warehouse_issue_order_id' => $issueOrder->id,
            'warehouse_item_id' => $this->warehouseItem->id,
            'quantity' => 10,
            'unit_price' => '100.00',
            'total_price' => '1000.00'
        ]);

        // Act
        $response = $this->getJson("/api/internal/warehouses/issue-orders/{$issueOrder->id}/items");

        // Assert
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'warehouse_issue_order_id',
                    'warehouse_item_id',
                    'quantity',
                    'unit_price',
                    'total_price',
                    'created_at',
                    'updated_at'
                ]
            ],
            'meta'
        ]);
    }

    public function test_updates_issue_order_item(): void
    {
        // Arrange
        $issueOrder = WarehouseIssueOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'held' => false
        ]);

        $item = WarehouseIssueOrderItem::factory()->create([
            'warehouse_issue_order_id' => $issueOrder->id,
            'warehouse_item_id' => $this->warehouseItem->id,
            'quantity' => 10,
            'unit_price' => '100.00',
            'total_price' => '1000.00'
        ]);

        $updateData = [
            'quantity' => 15,
            'unit_price' => '120.00',
            'total_price' => '1800.00',
            'comment' => 'Обновленный комментарий'
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/issue-order-items/{$item->id}", $updateData);

        // Assert
        $response->assertStatus(200);
        $response->assertJson(['message' => 'Позиция расходного ордера обновлена']);

        $this->assertDatabaseHas('warehouse_issue_order_items', [
            'id' => $item->id,
            'quantity' => 15,
            'unit_price' => '120.00',
            'total_price' => '1800.00',
            'comment' => 'Обновленный комментарий'
        ]);
    }

    public function test_deletes_issue_order_item(): void
    {
        // Arrange
        $issueOrder = WarehouseIssueOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'held' => false
        ]);

        $item = WarehouseIssueOrderItem::factory()->create([
            'warehouse_issue_order_id' => $issueOrder->id,
            'warehouse_item_id' => $this->warehouseItem->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/warehouses/issue-order-items/{$item->id}");

        // Assert
        $response->assertStatus(200);
        $response->assertJson(['message' => 'Позиция расходного ордера удалена']);

        $this->assertDatabaseMissing('warehouse_issue_order_items', [
            'id' => $item->id
        ]);
    }

    public function test_cannot_modify_held_order_items(): void
    {
        // Arrange - проведенный ордер
        $issueOrder = WarehouseIssueOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'held' => true
        ]);

        $itemData = [
            'warehouse_issue_order_id' => $issueOrder->id,
            'warehouse_item_id' => $this->warehouseItem->id,
            'quantity' => 10,
            'unit_price' => '150.00',
            'total_price' => '1500.00'
        ];

        // Act & Assert - нельзя добавить позицию
        $response = $this->postJson('/api/internal/warehouses/issue-order-items', $itemData);
        $response->assertStatus(422);
        $response->assertJsonFragment(['message' => 'Нельзя добавлять позиции в проведенный расходный ордер']);
    }

    public function test_automatically_updates_order_totals(): void
    {
        // Arrange - создаем ордер
        $issueOrder = WarehouseIssueOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'total_quantity' => 0,
            'total_cost' => '0.00'
        ]);

        // Act - добавляем несколько позиций
        $item1Data = [
            'warehouse_issue_order_id' => $issueOrder->id,
            'warehouse_item_id' => $this->warehouseItem->id,
            'quantity' => 10,
            'unit_price' => '100.00',
            'total_price' => '1000.00'
        ];

        $this->postJson('/api/internal/warehouses/issue-order-items', $item1Data);

        $item2Data = [
            'warehouse_issue_order_id' => $issueOrder->id,
            'warehouse_item_id' => $this->warehouseItem->id,
            'quantity' => 5,
            'unit_price' => '200.00',
            'total_price' => '1000.00'
        ];

        $this->postJson('/api/internal/warehouses/issue-order-items', $item2Data);

        // Assert - проверяем автоматический пересчет итогов
        $this->assertDatabaseHas('warehouse_issue_orders', [
            'id' => $issueOrder->id,
            'total_quantity' => 15,
            'total_cost' => '2000.00'
        ]);
    }

    public function test_validates_required_item_fields(): void
    {
        // Arrange
        $issueOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-15',
            'write_off_reason' => 'defective',
            'total_quantity' => 10,
            'total_cost' => '1500.00',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    // warehouse_item_id отсутствует
                    'quantity' => 10,
                    'unit_price' => '150.00',
                    'total_price' => '1500.00',
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/issue-orders', $issueOrderData);

        // Assert
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['items.0.warehouse_item_id']);
    }

    public function test_validates_warehouse_item_belongs_to_warehouse(): void
    {
        // Arrange - создаем товар на другом складе
        $otherWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $otherWarehouseItem = WarehouseItem::factory()->create([
            'warehouse_id' => $otherWarehouse->id,
            'product_id' => $this->product->id,
        ]);

        $issueOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-15',
            'write_off_reason' => 'defective',
            'total_quantity' => 10,
            'total_cost' => '1500.00',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'warehouse_item_id' => $otherWarehouseItem->id, // Товар с другого склада
                    'quantity' => 10,
                    'unit_price' => '150.00',
                    'total_price' => '1500.00',
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/issue-orders', $issueOrderData);

        // Assert
        $response->assertStatus(422);
    }

    public function test_validates_sufficient_quantity_available(): void
    {
        // Arrange - товар с недостаточным количеством
        $lowStockItem = WarehouseItem::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 5,
            'available_quantity' => 5,
        ]);

        $issueOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-15',
            'write_off_reason' => 'defective',
            'total_quantity' => 10,
            'total_cost' => '1500.00',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'warehouse_item_id' => $lowStockItem->id,
                    'quantity' => 10, // Больше чем доступно
                    'unit_price' => '150.00',
                    'total_price' => '1500.00',
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/issue-orders', $issueOrderData);

        // Assert
        $response->assertStatus(422);
    }

    public function test_cannot_write_off_reserved_items(): void
    {
        // Arrange - создаем резерв
        WarehouseReservation::factory()->create([
            'warehouse_item_id' => $this->warehouseItem->id,
            'reserved_quantity' => 50,
            'status' => 'reserved',
        ]);

        // Обновляем доступное количество
        $this->warehouseItem->update([
            'available_quantity' => 50,
            'reserved_quantity' => 50,
        ]);

        $issueOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-15',
            'write_off_reason' => 'defective',
            'total_quantity' => 60,
            'total_cost' => '9000.00',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'warehouse_item_id' => $this->warehouseItem->id,
                    'quantity' => 60, // Больше чем доступно (50)
                    'unit_price' => '150.00',
                    'total_price' => '9000.00',
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/issue-orders', $issueOrderData);

        // Assert
        $response->assertStatus(422);
    }

    public function test_updates_warehouse_items_when_order_is_held(): void
    {
        // Arrange
        $issueOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'number' => 'РО-HOLD',
            'date_from' => '2024-01-15',
            'write_off_reason' => 'defective',
            'total_quantity' => 20,
            'total_cost' => '3000.00',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'warehouse_item_id' => $this->warehouseItem->id,
                    'quantity' => 20,
                    'unit_price' => '150.00',
                    'total_price' => '3000.00',
                ]
            ]
        ];

        // Act - создаем ордер
        $createResponse = $this->postJson('/api/internal/warehouses/issue-orders', $issueOrderData);
        $createResponse->assertStatus(201);

        $issueOrder = WarehouseIssueOrder::where('number', 'РО-HOLD')->first();

        // Act - проводим ордер
        $holdResponse = $this->postJson("/api/internal/warehouses/issue-orders/{$issueOrder->id}/hold");

        // Assert
        $holdResponse->assertStatus(200);

        // Проверяем, что количество уменьшилось
        $this->warehouseItem->refresh();
        $this->assertEquals(80, $this->warehouseItem->quantity);
        $this->assertEquals(80, $this->warehouseItem->available_quantity);

        // Проверяем, что создались транзакции
        $this->assertDatabaseHas('warehouse_transactions', [
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'warehouse_item_id' => $this->warehouseItem->id,
            'transaction_type' => 'out',
            'quantity' => 20,
            'document_type' => 'warehouse_issue_order',
            'document_id' => $issueOrder->id,
        ]);
    }

    public function test_cannot_modify_items_of_held_order(): void
    {
        // Arrange - создаем проведенный ордер
        $heldOrder = WarehouseIssueOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'held' => true,
        ]);

        WarehouseIssueOrderItem::factory()->create([
            'issue_order_id' => $heldOrder->id,
            'product_id' => $this->product->id,
            'warehouse_item_id' => $this->warehouseItem->id,
        ]);

        $updateData = [
            'number' => 'UPDATED',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'warehouse_item_id' => $this->warehouseItem->id,
                    'quantity' => 50, // Пытаемся изменить количество
                    'unit_price' => '150.00',
                    'total_price' => '7500.00',
                ]
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/issue-orders/{$heldOrder->id}", $updateData);

        // Assert
        $response->assertStatus(422);
    }

    public function test_validates_positive_quantities(): void
    {
        // Arrange
        $issueOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-15',
            'write_off_reason' => 'defective',
            'total_quantity' => 10,
            'total_cost' => '1500.00',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'warehouse_item_id' => $this->warehouseItem->id,
                    'quantity' => -5, // Отрицательное количество
                    'unit_price' => '150.00',
                    'total_price' => '1500.00',
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/issue-orders', $issueOrderData);

        // Assert
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['items.0.quantity']);
    }

    public function test_validates_warehouse_item_exists(): void
    {
        // Arrange
        $issueOrderData = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-15',
            'write_off_reason' => 'defective',
            'total_quantity' => 10,
            'total_cost' => '1500.00',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'warehouse_item_id' => 'non-existent-uuid',
                    'quantity' => 10,
                    'unit_price' => '150.00',
                    'total_price' => '1500.00',
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/issue-orders', $issueOrderData);

        // Assert
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['items.0.warehouse_item_id']);
    }
}
