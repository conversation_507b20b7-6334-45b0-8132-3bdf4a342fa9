<?php

namespace Tests\Feature\WarehouseOrderScheme;

use App\Models\Warehouse;
use App\Models\WarehouseOrderScheme;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseOrderSchemeDetectionService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class WarehouseOrderSchemeDetectionIntegrationTest extends TestCase
{
    use RefreshDatabase;

    private WarehouseOrderSchemeDetectionService $service;
    private Warehouse $warehouse;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->service = app(WarehouseOrderSchemeDetectionService::class);
        
        $this->warehouse = Warehouse::factory()->create([
            'name' => 'Test Warehouse',
            'cabinet_id' => 'cabinet-123',
            'employee_id' => 'employee-123',
            'department_id' => 'department-123',
            'control_free_residuals' => true,
        ]);
    }

    public function test_warehouse_without_order_scheme_returns_regular_mode(): void
    {
        $result = $this->service->getOrderSchemeMode($this->warehouse->id);

        $expected = [
            'mode' => 'regular',
            'receipts_active' => false,
            'shipments_active' => false,
            'control_operational_balances' => false,
        ];

        $this->assertEquals($expected, $result);
    }

    public function test_warehouse_with_full_order_scheme_returns_correct_mode(): void
    {
        WarehouseOrderScheme::create([
            'warehouse_id' => $this->warehouse->id,
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => '2024-01-01',
            'control_operational_balances' => true,
        ]);

        Carbon::setTestNow('2024-01-02');
        $result = $this->service->getOrderSchemeMode($this->warehouse->id);

        $this->assertEquals('full_order_scheme', $result['mode']);
        $this->assertTrue($result['receipts_active']);
        $this->assertTrue($result['shipments_active']);
        $this->assertTrue($result['control_operational_balances']);
        $this->assertEquals('2024-01-01', $result['on_coming_from']);
        $this->assertEquals('2024-01-01', $result['on_shipment_from']);
        
        Carbon::setTestNow();
    }

    public function test_warehouse_with_receipts_only_order_scheme(): void
    {
        WarehouseOrderScheme::create([
            'warehouse_id' => $this->warehouse->id,
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => null,
            'control_operational_balances' => false,
        ]);

        Carbon::setTestNow('2024-01-02');
        $result = $this->service->getOrderSchemeMode($this->warehouse->id);

        $this->assertEquals('receipts_only', $result['mode']);
        $this->assertTrue($result['receipts_active']);
        $this->assertFalse($result['shipments_active']);
        $this->assertFalse($result['control_operational_balances']);
        
        Carbon::setTestNow();
    }

    public function test_warehouse_with_shipments_only_order_scheme(): void
    {
        WarehouseOrderScheme::create([
            'warehouse_id' => $this->warehouse->id,
            'on_coming_from' => null,
            'on_shipment_from' => '2024-01-01',
            'control_operational_balances' => true,
        ]);

        Carbon::setTestNow('2024-01-02');
        $result = $this->service->getOrderSchemeMode($this->warehouse->id);

        $this->assertEquals('shipments_only', $result['mode']);
        $this->assertFalse($result['receipts_active']);
        $this->assertTrue($result['shipments_active']);
        $this->assertTrue($result['control_operational_balances']);
        
        Carbon::setTestNow();
    }

    public function test_order_scheme_not_active_before_start_date(): void
    {
        WarehouseOrderScheme::create([
            'warehouse_id' => $this->warehouse->id,
            'on_coming_from' => '2024-01-02',
            'on_shipment_from' => '2024-01-02',
            'control_operational_balances' => true,
        ]);

        Carbon::setTestNow('2024-01-01');
        $result = $this->service->getOrderSchemeMode($this->warehouse->id);

        $this->assertEquals('regular', $result['mode']);
        $this->assertFalse($result['receipts_active']);
        $this->assertFalse($result['shipments_active']);
        
        Carbon::setTestNow();
    }

    public function test_order_scheme_active_on_start_date(): void
    {
        WarehouseOrderScheme::create([
            'warehouse_id' => $this->warehouse->id,
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => '2024-01-01',
            'control_operational_balances' => true,
        ]);

        Carbon::setTestNow('2024-01-01');
        $result = $this->service->getOrderSchemeMode($this->warehouse->id);

        $this->assertEquals('full_order_scheme', $result['mode']);
        $this->assertTrue($result['receipts_active']);
        $this->assertTrue($result['shipments_active']);
        
        Carbon::setTestNow();
    }

    public function test_is_order_scheme_active_for_receipts_with_specific_date(): void
    {
        WarehouseOrderScheme::create([
            'warehouse_id' => $this->warehouse->id,
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => null,
            'control_operational_balances' => false,
        ]);

        $this->assertTrue($this->service->isOrderSchemeActiveForReceipts($this->warehouse->id, '2024-01-01'));
        $this->assertTrue($this->service->isOrderSchemeActiveForReceipts($this->warehouse->id, '2024-01-02'));
        $this->assertFalse($this->service->isOrderSchemeActiveForReceipts($this->warehouse->id, '2023-12-31'));
    }

    public function test_is_order_scheme_active_for_shipments_with_specific_date(): void
    {
        WarehouseOrderScheme::create([
            'warehouse_id' => $this->warehouse->id,
            'on_coming_from' => null,
            'on_shipment_from' => '2024-01-01',
            'control_operational_balances' => true,
        ]);

        $this->assertTrue($this->service->isOrderSchemeActiveForShipments($this->warehouse->id, '2024-01-01'));
        $this->assertTrue($this->service->isOrderSchemeActiveForShipments($this->warehouse->id, '2024-01-02'));
        $this->assertFalse($this->service->isOrderSchemeActiveForShipments($this->warehouse->id, '2023-12-31'));
    }

    public function test_can_enable_order_scheme_when_no_scheme_exists(): void
    {
        $result = $this->service->canEnableOrderScheme($this->warehouse->id);

        $this->assertTrue($result['can_enable']);
        $this->assertEmpty($result['reasons']);
    }

    public function test_cannot_enable_order_scheme_when_scheme_already_exists(): void
    {
        WarehouseOrderScheme::create([
            'warehouse_id' => $this->warehouse->id,
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => null,
            'control_operational_balances' => false,
        ]);

        $result = $this->service->canEnableOrderScheme($this->warehouse->id);

        $this->assertFalse($result['can_enable']);
        $this->assertContains('Order scheme is already partially or fully enabled', $result['reasons']);
    }

    public function test_is_order_scheme_active_with_different_operation_types(): void
    {
        WarehouseOrderScheme::create([
            'warehouse_id' => $this->warehouse->id,
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => '2024-01-02',
            'control_operational_balances' => true,
        ]);

        Carbon::setTestNow('2024-01-01');
        
        $this->assertTrue($this->service->isOrderSchemeActive($this->warehouse->id, 'receipts'));
        $this->assertFalse($this->service->isOrderSchemeActive($this->warehouse->id, 'shipments'));
        $this->assertFalse($this->service->isOrderSchemeActive($this->warehouse->id, 'all'));
        $this->assertTrue($this->service->isOrderSchemeActive($this->warehouse->id, 'any'));

        Carbon::setTestNow('2024-01-02');
        
        $this->assertTrue($this->service->isOrderSchemeActive($this->warehouse->id, 'receipts'));
        $this->assertTrue($this->service->isOrderSchemeActive($this->warehouse->id, 'shipments'));
        $this->assertTrue($this->service->isOrderSchemeActive($this->warehouse->id, 'all'));
        $this->assertTrue($this->service->isOrderSchemeActive($this->warehouse->id, 'any'));
        
        Carbon::setTestNow();
    }

    public function test_get_warehouse_scheme_returns_correct_object(): void
    {
        $scheme = WarehouseOrderScheme::create([
            'warehouse_id' => $this->warehouse->id,
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => '2024-01-02',
            'control_operational_balances' => true,
        ]);

        $result = $this->service->getWarehouseScheme($this->warehouse->id);

        $this->assertNotNull($result);
        $this->assertEquals($scheme->warehouse_id, $result->warehouse_id);
        $this->assertEquals($scheme->on_coming_from, $result->on_coming_from);
        $this->assertEquals($scheme->on_shipment_from, $result->on_shipment_from);
        $this->assertEquals($scheme->control_operational_balances, $result->control_operational_balances);
    }

    public function test_get_warehouse_scheme_returns_null_when_no_scheme_exists(): void
    {
        $result = $this->service->getWarehouseScheme($this->warehouse->id);

        $this->assertNull($result);
    }
}
