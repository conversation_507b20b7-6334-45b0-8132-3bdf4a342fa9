<?php

namespace Tests\Feature\WarehouseReservation;

use App\Contracts\Services\Internal\WarehouseReservationServiceContract;
use App\Models\Cabinet;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Product;
use App\Models\Status;
use App\Models\Warehouse;
use App\Models\WarehouseItem;
use App\Models\WarehouseOrderScheme;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class WarehouseReservationServiceIntegrationTest extends TestCase
{
    use RefreshDatabase;

    private WarehouseReservationServiceContract $service;
    private Warehouse $warehouse;
    private Product $product;
    private Employee $employee;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = app(WarehouseReservationServiceContract::class);
        
        $cabinet = Cabinet::factory()->create();
        $department = Department::factory()->create();
        $this->employee = Employee::factory()->create();
        $status = Status::factory()->create();
        
        $this->warehouse = Warehouse::factory()->create([
            'cabinet_id' => $cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $department->id,
        ]);
        
        // Создаем ордерную схему для склада (вручную, так как нет фабрики)
        WarehouseOrderScheme::create([
            'warehouse_id' => $this->warehouse->id,
            'on_coming_from' => '2024-01-01',
            'on_shipment_from' => '2024-01-01',
            'control_operational_balances' => true,
        ]);
        
        $this->product = Product::factory()->create();
    }

    public function test_can_create_simple_reservation(): void
    {
        $data = [
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'document_type' => 'order',
            'document_id' => '550e8400-e29b-41d4-a716-************',
            'reservation_type' => 'order',
            'priority' => 5,
            'created_by' => $this->employee->id,
        ];

        $result = $this->service->createReservation($data);

        $this->assertTrue($result['success']);
        $this->assertNotNull($result['reservation_id']);
        $this->assertDatabaseHas('warehouse_reservations', [
            'id' => $result['reservation_id'],
            'quantity' => 100,
            'status' => 'active'
        ]);
    }

    public function test_validation_fails_for_invalid_data(): void
    {
        $data = [
            'warehouse_id' => 'invalid-uuid',
            'product_id' => $this->product->id,
            'quantity' => -10, // Отрицательное количество
            'document_type' => 'order',
            'document_id' => '550e8400-e29b-41d4-a716-************',
        ];

        $result = $this->service->createReservation($data);

        $this->assertFalse($result['valid']);
        $this->assertNotEmpty($result['errors']);
    }

    public function test_can_reserve_quantity_from_available_items(): void
    {
        // Создаем товары на складе
        WarehouseItem::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'reserved_quantity' => 0,
            'batch_number' => 'BATCH001',
            'quality_status' => 'good',
            'status' => 'in_stock',
        ]);

        WarehouseItem::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 50,
            'reserved_quantity' => 0,
            'batch_number' => 'BATCH002',
            'quality_status' => 'good',
            'status' => 'in_stock',
        ]);

        $options = [
            'document_type' => 'order',
            'document_id' => '550e8400-e29b-41d4-a716-************',
            'created_by' => $this->employee->id,
        ];

        $result = $this->service->reserveQuantity(
            $this->warehouse->id,
            $this->product->id,
            120, // Резервируем 120 из 150 доступных
            $options
        );

        $this->assertTrue($result['success']);
        $this->assertEquals(120, $result['reserved_quantity']);
        $this->assertCount(2, $result['reservations']); // Должно создать 2 резерва
    }

    public function test_cannot_reserve_more_than_available(): void
    {
        // Создаем только 50 единиц товара
        WarehouseItem::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 50,
            'reserved_quantity' => 0,
            'batch_number' => 'BATCH001',
            'quality_status' => 'good',
            'status' => 'in_stock',
        ]);

        $options = [
            'document_type' => 'order',
            'document_id' => '550e8400-e29b-41d4-a716-************',
            'created_by' => $this->employee->id,
        ];

        $result = $this->service->reserveQuantity(
            $this->warehouse->id,
            $this->product->id,
            100, // Пытаемся резервировать больше, чем есть
            $options
        );

        $this->assertFalse($result['success']);
        $this->assertStringContains('Missing:', $result['error']);
    }

    public function test_can_use_reservation(): void
    {
        // Создаем товар на складе
        $warehouseItem = WarehouseItem::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'reserved_quantity' => 0,
            'batch_number' => 'BATCH001',
            'quality_status' => 'good',
            'status' => 'in_stock',
        ]);

        // Создаем резерв
        $reservationResult = $this->service->reserveQuantity(
            $this->warehouse->id,
            $this->product->id,
            80,
            [
                'document_type' => 'order',
                'document_id' => '550e8400-e29b-41d4-a716-************',
                'created_by' => $this->employee->id,
            ]
        );

        $this->assertTrue($reservationResult['success']);
        $reservationId = $reservationResult['reservations'][0];

        // Используем резерв
        $useResult = $this->service->useReservation($reservationId, 30, [
            'document_type' => 'shipment',
            'document_id' => '550e8400-e29b-41d4-a716-446655440001',
        ]);

        $this->assertTrue($useResult['success']);
        $this->assertEquals(30, $useResult['used_quantity']);
        $this->assertEquals(50, $useResult['remaining_quantity']);

        // Проверяем, что количество на складе уменьшилось
        $warehouseItem->refresh();
        $this->assertEquals(70, $warehouseItem->quantity); // 100 - 30
        $this->assertEquals(50, $warehouseItem->reserved_quantity); // 80 - 30
    }

    public function test_cannot_use_more_than_reserved(): void
    {
        // Создаем товар и резерв
        WarehouseItem::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'reserved_quantity' => 0,
            'batch_number' => 'BATCH001',
            'quality_status' => 'good',
            'status' => 'in_stock',
        ]);

        $reservationResult = $this->service->reserveQuantity(
            $this->warehouse->id,
            $this->product->id,
            50,
            [
                'document_type' => 'order',
                'document_id' => '550e8400-e29b-41d4-a716-************',
                'created_by' => $this->employee->id,
            ]
        );

        $reservationId = $reservationResult['reservations'][0];

        // Пытаемся использовать больше, чем зарезервировано
        $useResult = $this->service->useReservation($reservationId, 80);

        $this->assertFalse($useResult['success']);
        $this->assertStringContains('Insufficient reserved quantity', $useResult['error']);
    }

    public function test_can_cancel_reservation(): void
    {
        // Создаем товар и резерв
        $warehouseItem = WarehouseItem::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'reserved_quantity' => 0,
            'batch_number' => 'BATCH001',
            'quality_status' => 'good',
            'status' => 'in_stock',
        ]);

        $reservationResult = $this->service->reserveQuantity(
            $this->warehouse->id,
            $this->product->id,
            60,
            [
                'document_type' => 'order',
                'document_id' => '550e8400-e29b-41d4-a716-************',
                'created_by' => $this->employee->id,
            ]
        );

        $reservationId = $reservationResult['reservations'][0];

        // Отменяем резерв
        $cancelResult = $this->service->cancelReservation(
            $reservationId,
            'Order cancelled',
            $this->employee->id
        );

        $this->assertTrue($cancelResult);

        // Проверяем, что резерв отменен
        $this->assertDatabaseHas('warehouse_reservations', [
            'id' => $reservationId,
            'status' => 'cancelled',
            'cancellation_reason' => 'Order cancelled'
        ]);

        // Проверяем, что зарезервированное количество освобождено
        $warehouseItem->refresh();
        $this->assertEquals(0, $warehouseItem->reserved_quantity);
    }

    public function test_fifo_reservation_logic(): void
    {
        // Создаем товары с разными датами поступления
        $oldItem = WarehouseItem::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 50,
            'reserved_quantity' => 0,
            'batch_number' => 'BATCH001',
            'received_at' => Carbon::now()->subDays(10),
            'quality_status' => 'good',
            'status' => 'in_stock',
        ]);

        $newItem = WarehouseItem::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 50,
            'reserved_quantity' => 0,
            'batch_number' => 'BATCH002',
            'received_at' => Carbon::now()->subDays(5),
            'quality_status' => 'good',
            'status' => 'in_stock',
        ]);

        // Резервируем 30 единиц
        $result = $this->service->reserveQuantity(
            $this->warehouse->id,
            $this->product->id,
            30,
            [
                'document_type' => 'order',
                'document_id' => '550e8400-e29b-41d4-a716-************',
                'created_by' => $this->employee->id,
            ]
        );

        $this->assertTrue($result['success']);

        // Проверяем, что резерв создался из старой партии (FIFO)
        $oldItem->refresh();
        $newItem->refresh();

        $this->assertEquals(30, $oldItem->reserved_quantity);
        $this->assertEquals(0, $newItem->reserved_quantity);
    }

    public function test_can_check_availability(): void
    {
        WarehouseItem::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'reserved_quantity' => 30,
            'batch_number' => 'BATCH001',
            'quality_status' => 'good',
            'status' => 'in_stock',
        ]);

        $result = $this->service->checkAvailability(
            $this->warehouse->id,
            $this->product->id,
            50
        );

        $this->assertTrue($result['available']);
        $this->assertEquals(50, $result['requested_quantity']);
        $this->assertEquals(70, $result['available_quantity']); // 100 - 30
        $this->assertEquals(30, $result['reserved_quantity']);
        $this->assertEquals(100, $result['total_quantity']);
        $this->assertEquals(0, $result['shortage']);
    }

    public function test_availability_check_shows_shortage(): void
    {
        WarehouseItem::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'reserved_quantity' => 80,
            'batch_number' => 'BATCH001',
            'quality_status' => 'good',
            'status' => 'in_stock',
        ]);

        $result = $this->service->checkAvailability(
            $this->warehouse->id,
            $this->product->id,
            50
        );

        $this->assertFalse($result['available']);
        $this->assertEquals(50, $result['requested_quantity']);
        $this->assertEquals(20, $result['available_quantity']); // 100 - 80
        $this->assertEquals(30, $result['shortage']); // 50 - 20
    }
}
