<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme;

use App\Contracts\Services\Internal\WarehouseOrderSchemeDetectionServiceContract;
use App\Services\Api\Internal\WarehouseOrderScheme\Handlers\SchemeDetectionHandler;

class WarehouseOrderSchemeDetectionService implements WarehouseOrderSchemeDetectionServiceContract
{
    public function __construct(
        private SchemeDetectionHandler $detectionHandler
    ) {
    }

    public function isOrderSchemeActiveForReceipts(string $warehouseId, ?string $date = null): bool
    {
        return $this->detectionHandler->isOrderSchemeActiveForReceipts($warehouseId, $date);
    }

    public function isOrderSchemeActiveForShipments(string $warehouseId, ?string $date = null): bool
    {
        return $this->detectionHandler->isOrderSchemeActiveForShipments($warehouseId, $date);
    }

    public function getOrderSchemeMode(string $warehouseId): array
    {
        return $this->detectionHandler->getOrderSchemeMode($warehouseId);
    }



    public function isOrderSchemeActive(string $warehouseId, string $operationType = 'all', ?string $date = null): bool
    {
        return $this->detectionHandler->isOrderSchemeActive($warehouseId, $operationType, $date);
    }

    public function getWarehouseScheme(string $warehouseId): ?object
    {
        return $this->detectionHandler->getWarehouseScheme($warehouseId);
    }
}
