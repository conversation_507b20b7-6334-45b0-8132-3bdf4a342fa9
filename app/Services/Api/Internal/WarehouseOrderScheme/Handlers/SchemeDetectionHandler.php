<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\Handlers;

use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseOrderSchemeDetectionService;

readonly class SchemeDetectionHandler
{
    public function __construct(
        private WarehouseOrderSchemeDetectionService $detectionService
    ) {
    }

    public function getWarehouseMode(string $warehouseId): array
    {
        return $this->detectionService->getOrderSchemeMode($warehouseId);
    }

    public function checkOperationAvailability(string $warehouseId, string $operationType, ?string $date = null): array
    {
        $mode = $this->detectionService->getOrderSchemeMode($warehouseId);
        
        $isActive = match ($operationType) {
            'receipt' => $mode['receipts_active'],
            'shipment' => $mode['shipments_active'],
            default => false,
        };

        return [
            'operation_type' => $operationType,
            'warehouse_id' => $warehouseId,
            'order_scheme_active' => $isActive,
            'mode' => $mode['mode'],
            'control_operational_balances' => $mode['control_operational_balances'],
            'date_checked' => $date ?: now()->toDateString(),
        ];
    }

    public function validateOrderSchemeOperation(string $warehouseId, string $operationType, array $data): array
    {
        $availability = $this->checkOperationAvailability($warehouseId, $operationType, $data['date'] ?? null);
        
        $errors = [];
        $warnings = [];

        if ($availability['order_scheme_active']) {
            if ($operationType === 'receipt' && empty($data['document_basis_type'])) {
                $warnings[] = 'Receipt operations in order scheme should have document basis';
            }

            if ($operationType === 'shipment' && empty($data['reservation_id'])) {
                if ($availability['control_operational_balances']) {
                    $errors[] = 'Shipment operations in order scheme require reservation';
                } else {
                    $warnings[] = 'Shipment operations in order scheme should have reservation';
                }
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings,
            'availability' => $availability,
        ];
    }
}
