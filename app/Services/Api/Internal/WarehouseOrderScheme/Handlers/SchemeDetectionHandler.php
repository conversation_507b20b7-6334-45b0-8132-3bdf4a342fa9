<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\Handlers;

use App\Entities\WarehouseOrderSchemeEntity;
use Carbon\Carbon;

class SchemeDetectionHandler
{
    public function __construct(
        private WarehouseOrderSchemeEntity $entity
    ) {
    }

    public function isOrderSchemeActiveForReceipts(string $warehouseId, ?string $date = null): bool
    {
        $checkDate = $date ? Carbon::parse($date) : Carbon::now();

        $scheme = $this->entity
            ->where('warehouse_id', $warehouseId)
            ->first();

        if (!$scheme) {
            return false;
        }

        return $scheme->on_coming_from &&
               Carbon::parse($scheme->on_coming_from)->lte($checkDate);
    }

    public function isOrderSchemeActiveForShipments(string $warehouseId, ?string $date = null): bool
    {
        $checkDate = $date ? Carbon::parse($date) : Carbon::now();

        $scheme = $this->entity
            ->where('warehouse_id', $warehouseId)
            ->first();

        if (!$scheme) {
            return false;
        }

        return $scheme->on_shipment_from &&
               Carbon::parse($scheme->on_shipment_from)->lte($checkDate);
    }

    public function getOrderSchemeMode(string $warehouseId): array
    {
        $scheme = $this->entity
            ->where('warehouse_id', $warehouseId)
            ->first();

        if (!$scheme) {
            return [
                'mode' => 'regular',
                'receipts_active' => false,
                'shipments_active' => false,
                'control_operational_balances' => false,
            ];
        }

        $receiptsActive = $this->isOrderSchemeActiveForReceipts($warehouseId);
        $shipmentsActive = $this->isOrderSchemeActiveForShipments($warehouseId);

        $mode = 'regular';
        if ($receiptsActive && $shipmentsActive) {
            $mode = 'full_order_scheme';
        } elseif ($receiptsActive) {
            $mode = 'receipts_only';
        } elseif ($shipmentsActive) {
            $mode = 'shipments_only';
        }

        return [
            'mode' => $mode,
            'receipts_active' => $receiptsActive,
            'shipments_active' => $shipmentsActive,
            'control_operational_balances' => (bool) $scheme->control_operational_balances,
            'on_coming_from' => $scheme->on_coming_from,
            'on_shipment_from' => $scheme->on_shipment_from,
        ];
    }

    public function isOrderSchemeActive(string $warehouseId, string $operationType = 'all', ?string $date = null): bool
    {
        return match ($operationType) {
            'receipts' => $this->isOrderSchemeActiveForReceipts($warehouseId, $date),
            'shipments' => $this->isOrderSchemeActiveForShipments($warehouseId, $date),
            'all' => $this->isOrderSchemeActiveForReceipts($warehouseId, $date) &&
                     $this->isOrderSchemeActiveForShipments($warehouseId, $date),
            'any' => $this->isOrderSchemeActiveForReceipts($warehouseId, $date) ||
                     $this->isOrderSchemeActiveForShipments($warehouseId, $date),
            default => false,
        };
    }

    public function getWarehouseScheme(string $warehouseId): ?object
    {
        return $this->entity
            ->where('warehouse_id', $warehouseId)
            ->first();
    }


}
