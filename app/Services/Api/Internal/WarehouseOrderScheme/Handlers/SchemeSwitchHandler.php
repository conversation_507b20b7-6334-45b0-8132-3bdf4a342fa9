<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme\Handlers;

use App\Contracts\Repositories\WarehouseOrderSchemesRepositoryContract;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseOrderSchemeDetectionService;
use Carbon\Carbon;
use InvalidArgumentException;

readonly class SchemeSwitchHandler
{
    public function __construct(
        private WarehouseOrderSchemeDetectionService $detectionService,
        private WarehouseOrderSchemesRepositoryContract $repository
    ) {
    }

    public function enableOrderScheme(string $warehouseId, array $settings): array
    {
        $canEnable = $this->detectionService->canEnableOrderScheme($warehouseId);
        
        if (!$canEnable['can_enable']) {
            throw new InvalidArgumentException('Cannot enable order scheme: ' . implode(', ', $canEnable['reasons']));
        }

        $this->validateSettings($settings);

        $existingScheme = $this->detectionService->getWarehouseScheme($warehouseId);
        
        $data = [
            'warehouse_id' => $warehouseId,
            'on_coming_from' => $settings['on_coming_from'] ?? null,
            'on_shipment_from' => $settings['on_shipment_from'] ?? null,
            'control_operational_balances' => $settings['control_operational_balances'] ?? true,
        ];

        if ($existingScheme) {
            $this->repository->updateByWarehouseId($warehouseId, $data);
        } else {
            $this->repository->insert(array_merge($data, [
                'created_at' => Carbon::now(),
            ]));
        }

        return [
            'success' => true,
            'warehouse_id' => $warehouseId,
            'settings' => $data,
            'mode' => $this->detectionService->getOrderSchemeMode($warehouseId),
        ];
    }

    public function updateOrderSchemeSettings(string $warehouseId, array $settings): array
    {
        $existingScheme = $this->detectionService->getWarehouseScheme($warehouseId);
        
        if (!$existingScheme) {
            throw new InvalidArgumentException('Order scheme not found for warehouse');
        }

        $this->validateSettings($settings);

        $this->repository->updateByWarehouseId($warehouseId, $settings);

        return [
            'success' => true,
            'warehouse_id' => $warehouseId,
            'updated_settings' => $settings,
            'mode' => $this->detectionService->getOrderSchemeMode($warehouseId),
        ];
    }

    public function disableOrderScheme(string $warehouseId, string $operationType = 'all'): array
    {
        $existingScheme = $this->detectionService->getWarehouseScheme($warehouseId);
        
        if (!$existingScheme) {
            throw new InvalidArgumentException('Order scheme not found for warehouse');
        }

        $updateData = [];

        switch ($operationType) {
            case 'receipts':
                $updateData['on_coming_from'] = null;
                break;
            case 'shipments':
                $updateData['on_shipment_from'] = null;
                break;
            case 'all':
                $updateData['on_coming_from'] = null;
                $updateData['on_shipment_from'] = null;
                break;
            default:
                throw new InvalidArgumentException('Invalid operation type');
        }

        $this->repository->updateByWarehouseId($warehouseId, $updateData);

        return [
            'success' => true,
            'warehouse_id' => $warehouseId,
            'disabled_operations' => $operationType,
            'mode' => $this->detectionService->getOrderSchemeMode($warehouseId),
        ];
    }

    private function validateSettings(array $settings): void
    {
        if (isset($settings['on_coming_from']) && $settings['on_coming_from']) {
            if (!Carbon::canBeCreatedFromFormat($settings['on_coming_from'], 'Y-m-d')) {
                throw new InvalidArgumentException('Invalid on_coming_from date format');
            }
        }

        if (isset($settings['on_shipment_from']) && $settings['on_shipment_from']) {
            if (!Carbon::canBeCreatedFromFormat($settings['on_shipment_from'], 'Y-m-d')) {
                throw new InvalidArgumentException('Invalid on_shipment_from date format');
            }
        }

        if (isset($settings['control_operational_balances']) && !is_bool($settings['control_operational_balances'])) {
            throw new InvalidArgumentException('control_operational_balances must be boolean');
        }
    }
}
