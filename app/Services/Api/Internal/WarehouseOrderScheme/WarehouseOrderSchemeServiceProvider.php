<?php

namespace App\Services\Api\Internal\WarehouseOrderScheme;

use App\Contracts\Services\Internal\WarehouseOrderSchemeDetectionServiceContract;
use Illuminate\Support\ServiceProvider;

class WarehouseOrderSchemeServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(WarehouseOrderSchemeDetectionServiceContract::class, WarehouseOrderSchemeDetectionService::class);
    }

    public function boot(): void
    {
        //
    }
}
