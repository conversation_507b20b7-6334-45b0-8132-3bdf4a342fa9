<?php

namespace App\Services\Api\Internal\WarehouseReservationService;

use App\Contracts\Services\Internal\WarehouseReservationServiceContract;
use App\Services\Api\Internal\WarehouseReservationService\Handlers\ReservationHandler;
use App\Services\Api\Internal\WarehouseReservationService\Handlers\AvailabilityHandler;
use App\Services\Api\Internal\WarehouseReservationService\Handlers\ValidationHandler;

class WarehouseReservationService implements WarehouseReservationServiceContract
{
    public function __construct(
        private ReservationHandler $reservationHandler,
        private AvailabilityHandler $availabilityHandler,
        private ValidationHandler $validationHandler
    ) {
    }

    public function createReservation(array $data): array
    {
        $validation = $this->validateReservationRequest($data);
        if (!$validation['valid']) {
            return $validation;
        }

        return $this->reservationHandler->createReservation($data);
    }

    public function reserveQuantity(string $warehouseId, string $productId, int $quantity, array $options = []): array
    {
        $availability = $this->checkAvailability($warehouseId, $productId, $quantity, $options);
        if (!$availability['available']) {
            return $availability;
        }

        return $this->reservationHandler->reserveQuantity($warehouseId, $productId, $quantity, $options);
    }

    public function useReservation(string $reservationId, int $quantity, array $options = []): array
    {
        return $this->reservationHandler->useReservation($reservationId, $quantity, $options);
    }

    public function cancelReservation(string $reservationId, string $reason = null, string $cancelledBy = null): bool
    {
        return $this->reservationHandler->cancelReservation($reservationId, $reason, $cancelledBy);
    }

    public function getAvailableQuantity(string $warehouseId, string $productId, array $filters = []): int
    {
        return $this->availabilityHandler->getAvailableQuantity($warehouseId, $productId, $filters);
    }

    public function getReservedQuantity(string $warehouseId, string $productId, array $filters = []): int
    {
        return $this->availabilityHandler->getReservedQuantity($warehouseId, $productId, $filters);
    }

    public function checkAvailability(string $warehouseId, string $productId, int $quantity, array $filters = []): array
    {
        return $this->availabilityHandler->checkAvailability($warehouseId, $productId, $quantity, $filters);
    }

    public function getReservationsByDocument(string $documentType, string $documentId): array
    {
        return $this->reservationHandler->getReservationsByDocument($documentType, $documentId);
    }

    public function expireOldReservations(): int
    {
        return $this->reservationHandler->expireOldReservations();
    }

    public function validateReservationRequest(array $data): array
    {
        return $this->validationHandler->validateReservationRequest($data);
    }
}
