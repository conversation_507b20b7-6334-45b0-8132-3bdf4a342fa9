<?php

namespace App\Services\Api\Internal\WarehouseReservationService\Handlers;

use App\Contracts\Repositories\WarehouseReservationsRepositoryContract;
use App\Contracts\Repositories\WarehouseItemsRepositoryContract;
use App\Contracts\Services\Internal\WarehouseTransactionServiceContract;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ReservationHandler
{
    public function __construct(
        private WarehouseReservationsRepositoryContract $reservationsRepository,
        private WarehouseItemsRepositoryContract $itemsRepository,
        private WarehouseTransactionServiceContract $transactionService
    ) {
    }

    public function createReservation(array $data): array
    {
        try {
            DB::beginTransaction();

            $reservationData = $this->prepareReservationData($data);
            $success = $this->reservationsRepository->insert($reservationData);

            if (!$success) {
                DB::rollBack();
                return [
                    'success' => false,
                    'error' => 'Failed to create reservation',
                    'reservation_id' => null
                ];
            }

            // Создаем транзакцию резервирования
            $this->transactionService->createReservationTransaction(
                $data['warehouse_id'],
                $data['product_id'],
                [
                    'quantity' => $data['quantity'],
                    'document_type' => $data['document_type'],
                    'document_id' => $data['document_id'],
                    'reservation_id' => $reservationData['id'],
                    'batch_number' => $data['batch_number'] ?? null,
                    'lot_number' => $data['lot_number'] ?? null,
                ]
            );

            DB::commit();

            return [
                'success' => true,
                'reservation_id' => $reservationData['id'],
                'message' => 'Reservation created successfully'
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'reservation_id' => null
            ];
        }
    }

    public function reserveQuantity(string $warehouseId, string $productId, int $quantity, array $options = []): array
    {
        try {
            DB::beginTransaction();

            // Находим доступные товары для резервирования (FIFO)
            $availableItems = $this->findAvailableItems($warehouseId, $productId, $quantity, $options);
            
            if (empty($availableItems)) {
                DB::rollBack();
                return [
                    'success' => false,
                    'error' => 'No available items for reservation',
                    'reservations' => []
                ];
            }

            $reservations = [];
            $remainingQuantity = $quantity;

            foreach ($availableItems as $item) {
                if ($remainingQuantity <= 0) {
                    break;
                }

                $reserveQuantity = min($remainingQuantity, $item->available_quantity);
                
                $reservationData = array_merge($options, [
                    'warehouse_id' => $warehouseId,
                    'product_id' => $productId,
                    'warehouse_item_id' => $item->id,
                    'quantity' => $reserveQuantity,
                    'reserved_quantity' => $reserveQuantity,
                    'batch_number' => $item->batch_number,
                    'lot_number' => $item->lot_number,
                    'expiry_date' => $item->expiry_date,
                ]);

                $result = $this->createReservation($reservationData);
                
                if ($result['success']) {
                    $reservations[] = $result['reservation_id'];
                    $remainingQuantity -= $reserveQuantity;

                    // Обновляем reserved_quantity в warehouse_items
                    $this->itemsRepository->update($item->id, [
                        'reserved_quantity' => $item->reserved_quantity + $reserveQuantity
                    ]);
                }
            }

            if ($remainingQuantity > 0) {
                DB::rollBack();
                return [
                    'success' => false,
                    'error' => "Could not reserve full quantity. Missing: {$remainingQuantity}",
                    'reservations' => []
                ];
            }

            DB::commit();

            return [
                'success' => true,
                'reservations' => $reservations,
                'reserved_quantity' => $quantity,
                'message' => 'Quantity reserved successfully'
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'reservations' => []
            ];
        }
    }

    public function useReservation(string $reservationId, int $quantity, array $options = []): array
    {
        try {
            DB::beginTransaction();

            $reservation = $this->reservationsRepository->findById($reservationId);
            
            if (!$reservation) {
                DB::rollBack();
                return [
                    'success' => false,
                    'error' => 'Reservation not found'
                ];
            }

            $availableQuantity = $reservation->reserved_quantity - $reservation->used_quantity;
            
            if ($availableQuantity < $quantity) {
                DB::rollBack();
                return [
                    'success' => false,
                    'error' => "Insufficient reserved quantity. Available: {$availableQuantity}, requested: {$quantity}"
                ];
            }

            // Обновляем использованное количество
            $this->reservationsRepository->update($reservationId, [
                'used_quantity' => $reservation->used_quantity + $quantity,
                'status' => ($reservation->used_quantity + $quantity >= $reservation->reserved_quantity) ? 'fulfilled' : 'active'
            ]);

            // Создаем транзакцию использования резерва
            $this->transactionService->createIssueTransaction(
                $reservation->warehouse_id,
                $reservation->product_id,
                array_merge($options, [
                    'quantity' => $quantity,
                    'reservation_id' => $reservationId,
                    'warehouse_item_id' => $reservation->warehouse_item_id,
                    'batch_number' => $reservation->batch_number,
                    'lot_number' => $reservation->lot_number,
                ])
            );

            // Обновляем warehouse_items
            if ($reservation->warehouse_item_id) {
                $item = $this->itemsRepository->findById($reservation->warehouse_item_id);
                if ($item) {
                    $this->itemsRepository->update($reservation->warehouse_item_id, [
                        'quantity' => $item->quantity - $quantity,
                        'reserved_quantity' => $item->reserved_quantity - $quantity,
                        'last_movement_date' => Carbon::now()
                    ]);
                }
            }

            DB::commit();

            return [
                'success' => true,
                'used_quantity' => $quantity,
                'remaining_quantity' => $availableQuantity - $quantity,
                'message' => 'Reservation used successfully'
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    public function cancelReservation(string $reservationId, string $reason = null, string $cancelledBy = null): bool
    {
        try {
            DB::beginTransaction();

            $reservation = $this->reservationsRepository->findById($reservationId);
            
            if (!$reservation) {
                DB::rollBack();
                return false;
            }

            // Освобождаем зарезервированное количество
            if ($reservation->warehouse_item_id) {
                $item = $this->itemsRepository->findById($reservation->warehouse_item_id);
                if ($item) {
                    $releaseQuantity = $reservation->reserved_quantity - $reservation->used_quantity;
                    $this->itemsRepository->update($reservation->warehouse_item_id, [
                        'reserved_quantity' => $item->reserved_quantity - $releaseQuantity
                    ]);
                }
            }

            // Обновляем статус резерва
            $this->reservationsRepository->update($reservationId, [
                'status' => 'cancelled',
                'cancelled_at' => Carbon::now(),
                'cancelled_by' => $cancelledBy,
                'cancellation_reason' => $reason
            ]);

            // Создаем транзакцию снятия резерва
            $releaseQuantity = $reservation->reserved_quantity - $reservation->used_quantity;
            if ($releaseQuantity > 0) {
                $this->transactionService->createReservationTransaction(
                    $reservation->warehouse_id,
                    $reservation->product_id,
                    [
                        'quantity' => -$releaseQuantity, // Отрицательное количество для снятия резерва
                        'reservation_id' => $reservationId,
                        'warehouse_item_id' => $reservation->warehouse_item_id,
                        'batch_number' => $reservation->batch_number,
                        'lot_number' => $reservation->lot_number,
                    ]
                );
            }

            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            return false;
        }
    }

    public function getReservationsByDocument(string $documentType, string $documentId): array
    {
        return $this->reservationsRepository->getByDocument($documentType, $documentId)->toArray();
    }

    public function expireOldReservations(): int
    {
        $expiredReservations = $this->reservationsRepository->getExpiredReservations();
        $count = 0;

        foreach ($expiredReservations as $reservation) {
            if ($this->cancelReservation($reservation->id, 'Expired automatically')) {
                $count++;
            }
        }

        return $count;
    }

    private function prepareReservationData(array $data): array
    {
        $defaults = [
            'reserved_at' => Carbon::now(),
            'status' => 'active',
            'priority' => 5,
            'reservation_type' => 'order',
            'reserved_quantity' => 0,
            'used_quantity' => 0,
        ];

        return array_merge($defaults, $data);
    }

    private function findAvailableItems(string $warehouseId, string $productId, int $quantity, array $options = []): array
    {
        $filters = [
            'warehouse_id' => $warehouseId,
            'product_id' => $productId,
            'available_quantity_min' => 1
        ];

        // Добавляем фильтры из опций
        if (isset($options['batch_number'])) {
            $filters['batch_number'] = $options['batch_number'];
        }

        if (isset($options['quality_status'])) {
            $filters['quality_status'] = $options['quality_status'];
        }

        if (isset($options['expiry_after'])) {
            $filters['expiry_after'] = $options['expiry_after'];
        }

        return $this->itemsRepository->getAvailableForReservation($filters)->toArray();
    }
}
