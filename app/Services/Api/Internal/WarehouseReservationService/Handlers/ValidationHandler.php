<?php

namespace App\Services\Api\Internal\WarehouseReservationService\Handlers;

use App\Contracts\Services\Internal\WarehouseOrderSchemeDetectionServiceContract;
use Carbon\Carbon;

class ValidationHandler
{
    public function __construct(
        private WarehouseOrderSchemeDetectionServiceContract $orderSchemeService
    ) {
    }

    public function validateReservationRequest(array $data): array
    {
        $errors = [];

        // Обязательные поля
        $requiredFields = ['warehouse_id', 'product_id', 'quantity', 'document_type', 'document_id'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $errors[] = "Field '{$field}' is required";
            }
        }

        // Валидация количества
        if (isset($data['quantity'])) {
            if (!is_numeric($data['quantity']) || $data['quantity'] <= 0) {
                $errors[] = 'Quantity must be a positive number';
            }

            if ($data['quantity'] > 1000000) {
                $errors[] = 'Quantity is too large (max: 1,000,000)';
            }
        }

        // Валидация приоритета
        if (isset($data['priority'])) {
            if (!is_numeric($data['priority']) || $data['priority'] < 1 || $data['priority'] > 10) {
                $errors[] = 'Priority must be between 1 and 10';
            }
        }

        // Валидация типа резерва
        if (isset($data['reservation_type'])) {
            $allowedTypes = ['order', 'production', 'transfer', 'marketing', 'quality'];
            if (!in_array($data['reservation_type'], $allowedTypes)) {
                $errors[] = 'Invalid reservation type';
            }
        }

        // Валидация срока действия
        if (isset($data['expires_at'])) {
            try {
                $expiresAt = Carbon::parse($data['expires_at']);
                if ($expiresAt->isPast()) {
                    $errors[] = 'Expiration date cannot be in the past';
                }
                
                if ($expiresAt->diffInDays(Carbon::now()) > 365) {
                    $errors[] = 'Expiration date cannot be more than 1 year in the future';
                }
            } catch (\Exception $e) {
                $errors[] = 'Invalid expiration date format';
            }
        }

        // Валидация UUID полей
        $uuidFields = ['warehouse_id', 'product_id', 'document_id', 'warehouse_item_id', 'created_by'];
        foreach ($uuidFields as $field) {
            if (isset($data[$field]) && !$this->isValidUuid($data[$field])) {
                $errors[] = "Field '{$field}' must be a valid UUID";
            }
        }

        // Проверка активности ордерной схемы
        if (isset($data['warehouse_id'])) {
            $schemeMode = $this->orderSchemeService->getOrderSchemeMode($data['warehouse_id']);
            
            if ($schemeMode['mode'] === 'regular') {
                $errors[] = 'Warehouse does not support order scheme reservations';
            }
            
            if (!$schemeMode['receipts_active'] && !$schemeMode['shipments_active']) {
                $errors[] = 'Order scheme is not active for this warehouse';
            }
        }

        // Валидация партионной информации
        if (isset($data['batch_number']) && strlen($data['batch_number']) > 50) {
            $errors[] = 'Batch number is too long (max: 50 characters)';
        }

        if (isset($data['lot_number']) && strlen($data['lot_number']) > 50) {
            $errors[] = 'Lot number is too long (max: 50 characters)';
        }

        // Валидация срока годности
        if (isset($data['expiry_date'])) {
            try {
                $expiryDate = Carbon::parse($data['expiry_date']);
                if ($expiryDate->isPast()) {
                    $errors[] = 'Cannot reserve expired products';
                }
            } catch (\Exception $e) {
                $errors[] = 'Invalid expiry date format';
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'message' => empty($errors) ? 'Validation passed' : 'Validation failed'
        ];
    }

    public function validateReservationUse(array $data): array
    {
        $errors = [];

        // Обязательные поля
        if (!isset($data['quantity']) || !is_numeric($data['quantity']) || $data['quantity'] <= 0) {
            $errors[] = 'Valid quantity is required';
        }

        // Валидация документа-основания для использования
        if (isset($data['document_type']) && isset($data['document_id'])) {
            if (!$this->isValidUuid($data['document_id'])) {
                $errors[] = 'Document ID must be a valid UUID';
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'message' => empty($errors) ? 'Validation passed' : 'Validation failed'
        ];
    }

    public function validateCancellation(array $data): array
    {
        $errors = [];

        // Причина отмены (опционально, но если есть - проверяем длину)
        if (isset($data['reason']) && strlen($data['reason']) > 500) {
            $errors[] = 'Cancellation reason is too long (max: 500 characters)';
        }

        // Ответственный за отмену
        if (isset($data['cancelled_by']) && !$this->isValidUuid($data['cancelled_by'])) {
            $errors[] = 'Cancelled by must be a valid UUID';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'message' => empty($errors) ? 'Validation passed' : 'Validation failed'
        ];
    }

    private function isValidUuid(string $uuid): bool
    {
        return preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i', $uuid) === 1;
    }
}
