<?php

namespace App\Services\Api\Internal\WarehouseReservationService\Handlers;

use App\Contracts\Repositories\WarehouseReservationsRepositoryContract;
use App\Contracts\Repositories\WarehouseItemsRepositoryContract;

class AvailabilityHandler
{
    public function __construct(
        private WarehouseReservationsRepositoryContract $reservationsRepository,
        private WarehouseItemsRepositoryContract $itemsRepository
    ) {
    }

    public function getAvailableQuantity(string $warehouseId, string $productId, array $filters = []): int
    {
        // Получаем общее количество товара на складе
        $totalQuantity = $this->itemsRepository->getTotalQuantity($productId, $warehouseId);
        
        // Получаем зарезервированное количество
        $reservedQuantity = $this->getReservedQuantity($warehouseId, $productId, $filters);
        
        return max(0, $totalQuantity - $reservedQuantity);
    }

    public function getReservedQuantity(string $warehouseId, string $productId, array $filters = []): int
    {
        return $this->reservationsRepository->getReservedQuantity($warehouseId, $productId, $filters);
    }

    public function checkAvailability(string $warehouseId, string $productId, int $quantity, array $filters = []): array
    {
        $availableQuantity = $this->getAvailableQuantity($warehouseId, $productId, $filters);
        $reservedQuantity = $this->getReservedQuantity($warehouseId, $productId, $filters);
        $totalQuantity = $this->itemsRepository->getTotalQuantity($productId, $warehouseId);

        $result = [
            'available' => $availableQuantity >= $quantity,
            'requested_quantity' => $quantity,
            'available_quantity' => $availableQuantity,
            'reserved_quantity' => $reservedQuantity,
            'total_quantity' => $totalQuantity,
            'shortage' => max(0, $quantity - $availableQuantity),
            'details' => []
        ];

        if (!$result['available']) {
            $result['error'] = "Insufficient quantity. Available: {$availableQuantity}, requested: {$quantity}";
            
            // Детализация по партиям
            $result['details'] = $this->getAvailabilityDetails($warehouseId, $productId, $filters);
        }

        return $result;
    }

    public function getAvailabilityDetails(string $warehouseId, string $productId, array $filters = []): array
    {
        $items = $this->itemsRepository->getByWarehouseAndProduct($warehouseId, $productId, $filters);
        $details = [];

        foreach ($items as $item) {
            $availableQuantity = $item->quantity - $item->reserved_quantity;
            
            $details[] = [
                'warehouse_item_id' => $item->id,
                'batch_number' => $item->batch_number,
                'lot_number' => $item->lot_number,
                'expiry_date' => $item->expiry_date,
                'quality_status' => $item->quality_status,
                'total_quantity' => $item->quantity,
                'reserved_quantity' => $item->reserved_quantity,
                'available_quantity' => $availableQuantity,
                'storage_location' => $item->storage_location
            ];
        }

        // Сортируем по FIFO (сначала старые партии)
        usort($details, function ($a, $b) {
            // Сначала по сроку годности (если есть)
            if ($a['expiry_date'] && $b['expiry_date']) {
                $expiryCmp = strcmp($a['expiry_date'], $b['expiry_date']);
                if ($expiryCmp !== 0) {
                    return $expiryCmp;
                }
            }
            
            // Потом по номеру партии
            return strcmp($a['batch_number'] ?? '', $b['batch_number'] ?? '');
        });

        return $details;
    }

    public function canReserveQuantity(string $warehouseId, string $productId, int $quantity, array $options = []): array
    {
        $filters = [];
        
        // Применяем фильтры из опций
        if (isset($options['batch_number'])) {
            $filters['batch_number'] = $options['batch_number'];
        }

        if (isset($options['quality_status'])) {
            $filters['quality_status'] = $options['quality_status'];
        } else {
            $filters['quality_status'] = 'good'; // По умолчанию только качественные товары
        }

        if (isset($options['expiry_after'])) {
            $filters['expiry_after'] = $options['expiry_after'];
        }

        if (isset($options['storage_location'])) {
            $filters['storage_location'] = $options['storage_location'];
        }

        $availability = $this->checkAvailability($warehouseId, $productId, $quantity, $filters);
        
        if (!$availability['available']) {
            return [
                'can_reserve' => false,
                'reason' => $availability['error'],
                'availability' => $availability
            ];
        }

        // Проверяем возможность резервирования по партиям (FIFO)
        $reservationPlan = $this->planReservation($warehouseId, $productId, $quantity, $options);
        
        return [
            'can_reserve' => $reservationPlan['possible'],
            'reason' => $reservationPlan['possible'] ? 'Can reserve' : $reservationPlan['error'],
            'reservation_plan' => $reservationPlan['plan'],
            'availability' => $availability
        ];
    }

    private function planReservation(string $warehouseId, string $productId, int $quantity, array $options = []): array
    {
        $filters = array_merge($options, [
            'available_quantity_min' => 1
        ]);

        $availableItems = $this->itemsRepository->getAvailableForReservation([
            'warehouse_id' => $warehouseId,
            'product_id' => $productId
        ] + $filters);

        $plan = [];
        $remainingQuantity = $quantity;

        foreach ($availableItems as $item) {
            if ($remainingQuantity <= 0) {
                break;
            }

            $availableQuantity = $item->quantity - $item->reserved_quantity;
            $reserveQuantity = min($remainingQuantity, $availableQuantity);

            $plan[] = [
                'warehouse_item_id' => $item->id,
                'batch_number' => $item->batch_number,
                'lot_number' => $item->lot_number,
                'expiry_date' => $item->expiry_date,
                'quality_status' => $item->quality_status,
                'available_quantity' => $availableQuantity,
                'reserve_quantity' => $reserveQuantity,
                'storage_location' => $item->storage_location
            ];

            $remainingQuantity -= $reserveQuantity;
        }

        return [
            'possible' => $remainingQuantity <= 0,
            'error' => $remainingQuantity > 0 ? "Cannot reserve {$remainingQuantity} units" : null,
            'plan' => $plan,
            'total_planned' => $quantity - $remainingQuantity
        ];
    }
}
