<?php

namespace App\Services\Api\Internal\WarehouseTransactionService;

use App\Contracts\Repositories\WarehouseTransactionsRepositoryContract;
use App\Contracts\Services\Internal\WarehouseTransactionServiceContract;
use App\Services\Api\Internal\WarehouseTransactionService\Handlers\TransactionHandler;

class WarehouseTransactionService implements WarehouseTransactionServiceContract
{
    public function __construct(
        private TransactionHandler $transactionHandler
    ) {
    }

    public function createTransaction(array $data): string
    {
        return $this->transactionHandler->createTransaction($data);
    }

    public function createReceiptTransaction(string $warehouseId, string $productId, array $data): string
    {
        return $this->transactionHandler->createReceiptTransaction($warehouseId, $productId, $data);
    }

    public function createIssueTransaction(string $warehouseId, string $productId, array $data): string
    {
        return $this->transactionHandler->createIssueTransaction($warehouseId, $productId, $data);
    }

    public function createReservationTransaction(string $warehouseId, string $productId, array $data): string
    {
        return $this->transactionHandler->createReservationTransaction($warehouseId, $productId, $data);
    }

    public function createAdjustmentTransaction(string $warehouseId, string $productId, array $data): string
    {
        return $this->transactionHandler->createAdjustmentTransaction($warehouseId, $productId, $data);
    }

    public function getTransactionsByDocument(string $documentType, string $documentId): array
    {
        return $this->transactionHandler->getTransactionsByDocument($documentType, $documentId);
    }

    public function getTransactionsByWarehouse(string $warehouseId, array $filters = []): array
    {
        return $this->transactionHandler->getTransactionsByWarehouse($warehouseId, $filters);
    }

    public function getTransactionsByProduct(string $productId, array $filters = []): array
    {
        return $this->transactionHandler->getTransactionsByProduct($productId, $filters);
    }

    public function calculateBalance(string $warehouseId, string $productId, array $filters = []): array
    {
        return $this->transactionHandler->calculateBalance($warehouseId, $productId, $filters);
    }
}
