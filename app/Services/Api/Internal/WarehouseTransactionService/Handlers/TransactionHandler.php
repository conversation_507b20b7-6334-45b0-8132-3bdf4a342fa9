<?php

namespace App\Services\Api\Internal\WarehouseTransactionService\Handlers;

use App\Contracts\Repositories\WarehouseTransactionsRepositoryContract;
use Carbon\Carbon;

class TransactionHandler
{
    public function __construct(
        private WarehouseTransactionsRepositoryContract $repository
    ) {
    }

    public function createTransaction(array $data): string
    {
        $transactionData = $this->prepareTransactionData($data);
        return $this->repository->insert($transactionData);
    }

    public function createReceiptTransaction(string $warehouseId, string $productId, array $data): string
    {
        $transactionData = array_merge($data, [
            'warehouse_id' => $warehouseId,
            'product_id' => $productId,
            'operation_type' => 'receipt',
            'quantity' => abs($data['quantity']), // Приход всегда положительный
        ]);

        return $this->createTransaction($transactionData);
    }

    public function createIssueTransaction(string $warehouseId, string $productId, array $data): string
    {
        $transactionData = array_merge($data, [
            'warehouse_id' => $warehouseId,
            'product_id' => $productId,
            'operation_type' => 'issue',
            'quantity' => -abs($data['quantity']), // Расход всегда отрицательный
        ]);

        return $this->createTransaction($transactionData);
    }

    public function createReservationTransaction(string $warehouseId, string $productId, array $data): string
    {
        $operationType = $data['quantity'] > 0 ? 'reserve' : 'unreserve';
        
        $transactionData = array_merge($data, [
            'warehouse_id' => $warehouseId,
            'product_id' => $productId,
            'operation_type' => $operationType,
        ]);

        return $this->createTransaction($transactionData);
    }

    public function createAdjustmentTransaction(string $warehouseId, string $productId, array $data): string
    {
        $operationType = $data['quantity'] > 0 ? 'adjustment_plus' : 'adjustment_minus';
        
        $transactionData = array_merge($data, [
            'warehouse_id' => $warehouseId,
            'product_id' => $productId,
            'operation_type' => $operationType,
        ]);

        return $this->createTransaction($transactionData);
    }

    public function getTransactionsByDocument(string $documentType, string $documentId): array
    {
        return $this->repository->getByDocument($documentType, $documentId)->toArray();
    }

    public function getTransactionsByWarehouse(string $warehouseId, array $filters = []): array
    {
        return $this->repository->getByWarehouse($warehouseId, $filters)->toArray();
    }

    public function getTransactionsByProduct(string $productId, array $filters = []): array
    {
        return $this->repository->getByProduct($productId, $filters)->toArray();
    }

    public function calculateBalance(string $warehouseId, string $productId, array $filters = []): array
    {
        $transactions = $this->repository->getByWarehouseAndProduct($warehouseId, $productId, $filters);
        
        $balance = [
            'total_quantity' => 0,
            'reserved_quantity' => 0,
            'available_quantity' => 0,
            'total_cost' => '0',
            'average_cost' => '0',
        ];

        foreach ($transactions as $transaction) {
            if (in_array($transaction->operation_type, ['receipt', 'adjustment_plus', 'transfer_in'])) {
                $balance['total_quantity'] += $transaction->quantity;
            } elseif (in_array($transaction->operation_type, ['issue', 'adjustment_minus', 'transfer_out'])) {
                $balance['total_quantity'] += $transaction->quantity; // quantity уже отрицательное
            } elseif ($transaction->operation_type === 'reserve') {
                $balance['reserved_quantity'] += $transaction->quantity;
            } elseif ($transaction->operation_type === 'unreserve') {
                $balance['reserved_quantity'] += $transaction->quantity; // quantity уже отрицательное
            }
        }

        $balance['available_quantity'] = $balance['total_quantity'] - $balance['reserved_quantity'];

        return $balance;
    }

    private function prepareTransactionData(array $data): array
    {
        $defaults = [
            'quality_status' => 'good',
            'transaction_date' => Carbon::now(),
        ];

        return array_merge($defaults, $data);
    }
}
