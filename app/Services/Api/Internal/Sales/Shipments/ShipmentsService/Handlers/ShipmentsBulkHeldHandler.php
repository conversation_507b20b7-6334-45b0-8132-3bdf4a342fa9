<?php

namespace App\Services\Api\Internal\Sales\Shipments\ShipmentsService\Handlers;

use App\Contracts\Repositories\ShipmentItemsRepositoryContract;
use App\Contracts\Repositories\ShipmentsRepositoryContract;
use App\Jobs\BulkHandleFifoJob;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Queue;

readonly class ShipmentsBulkHeldHandler
{
    public function __construct(
        private ShipmentsRepositoryContract $shipmentsRepository,
        private ShipmentItemsRepositoryContract $shipmentItemsRepository,
    ) {
    }

    /**
     * @throws BindingResolutionException
     */
    public function run(array $resourceIds): void
    {
        DB::table('shipments')
            ->whereIn('id', $resourceIds)
            ->update([
                'held' => true,
                'updated_at' => Carbon::now()
            ]);

        $shipmentItems = DB::table('shipment_items')
            ->join('shipments', 'shipments.id', '=', 'shipment_items.shipment_id')
            ->whereIn('shipment_id', $resourceIds)
            ->select([
                'shipment_items.id as shipment_item_id',
                'shipments.cabinet_id as cabinet_id',
                'shipment_items.product_id as product_id'
            ])
            ->get();

        if ($shipmentItems->isNotEmpty()) {
            // Используем BulkHandleFifoJob для оптимизированного пересчета
            Queue::push(new BulkHandleFifoJob($shipmentItems));
        }
    }
}
