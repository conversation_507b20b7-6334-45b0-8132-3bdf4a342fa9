<?php

namespace App\Services\Api\Internal\Workspace\StatusesService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;

class StatusDTO implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    public function __construct(
        public string $name,
        public string $color,
        public ?int $type,
        public ?string $resourceId = null,
        public ?string $cabinetId = null,
        public int $sort = 0
    ) {
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'cabinet_id' => $this->cabinetId,
            'name' => $this->name,
            'color' => $this->color,
            'type' => $this->type,
            'sort' => $this->sort
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'name' => $this->name,
            'color' => $this->color,
            'sort' => $this->sort
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            name: $data['name'],
            color: $data['color'],
            type: $data['type'] ?? null,
            resourceId: $data['resource_id'] ?? null,
            cabinetId: $data['cabinet_id'] ?? null,
            sort: $data['sort'] ?? 0
        );
    }
}
