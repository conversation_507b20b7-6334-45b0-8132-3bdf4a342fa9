<?php

namespace App\Contracts\Services\Internal;

interface WarehouseOrderSchemeDetectionServiceContract
{
    public function isOrderSchemeActiveForReceipts(string $warehouseId, ?string $date = null): bool;

    public function isOrderSchemeActiveForShipments(string $warehouseId, ?string $date = null): bool;

    public function getOrderSchemeMode(string $warehouseId): array;

    public function canEnableOrderScheme(string $warehouseId): array;

    public function isOrderSchemeActive(string $warehouseId, string $operationType = 'all', ?string $date = null): bool;

    public function getWarehouseScheme(string $warehouseId): ?object;
}
