<?php

namespace App\Contracts\Services\Internal;

interface WarehouseTransactionServiceContract
{
    public function createTransaction(array $data): string;
    
    public function createReceiptTransaction(string $warehouseId, string $productId, array $data): string;
    
    public function createIssueTransaction(string $warehouseId, string $productId, array $data): string;
    
    public function createReservationTransaction(string $warehouseId, string $productId, array $data): string;
    
    public function createAdjustmentTransaction(string $warehouseId, string $productId, array $data): string;
    
    public function getTransactionsByDocument(string $documentType, string $documentId): array;
    
    public function getTransactionsByWarehouse(string $warehouseId, array $filters = []): array;
    
    public function getTransactionsByProduct(string $productId, array $filters = []): array;
    
    public function calculateBalance(string $warehouseId, string $productId, array $filters = []): array;
}
