<?php

namespace App\Contracts\Services\Internal;

interface WarehouseReservationServiceContract
{
    public function createReservation(array $data): array;
    
    public function reserveQuantity(string $warehouseId, string $productId, int $quantity, array $options = []): array;
    
    public function useReservation(string $reservationId, int $quantity, array $options = []): array;
    
    public function cancelReservation(string $reservationId, string $reason = null, string $cancelledBy = null): bool;
    
    public function getAvailableQuantity(string $warehouseId, string $productId, array $filters = []): int;
    
    public function getReservedQuantity(string $warehouseId, string $productId, array $filters = []): int;
    
    public function checkAvailability(string $warehouseId, string $productId, int $quantity, array $filters = []): array;
    
    public function getReservationsByDocument(string $documentType, string $documentId): array;
    
    public function expireOldReservations(): int;
    
    public function validateReservationRequest(array $data): array;
}
