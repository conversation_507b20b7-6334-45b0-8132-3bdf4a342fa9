<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;

interface WarehouseReceiptOrdersRepositoryContract
{
    public function insert(array $data): bool;
    
    public function update(string $id, array $data): int;
    
    public function delete(string $id): int;
    
    public function findById(string $id): ?object;
    
    public function getByWarehouse(string $warehouseId, array $filters = []): Collection;
    
    public function getByDocument(string $documentType, string $documentId): Collection;
    
    public function getByDateRange(string $warehouseId, string $dateFrom, string $dateTo): Collection;
}
