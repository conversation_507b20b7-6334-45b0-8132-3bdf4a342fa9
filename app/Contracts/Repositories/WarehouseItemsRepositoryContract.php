<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;

interface WarehouseItemsRepositoryContract
{
    public function bulkUpdate(array $acceptanceIds, string $productId, array $data): int;
    public function getShipmentItemIdByAcceptance(string $acceptanceId, string $productId): ?object;

    public function deleteWhereAcceptanceAndProductIds(string $acceptanceId, array $productId): void;

    public function getShipmentItemIdByAcceptanceAndProductIds(string $acceptanceId, array $productIds): ?string;
    public function create(array $data): void;

    public function upsert(array $data, array $updateColumns = null): void;

    public function deleteWhereInIds(array $ids): void;

    public function returnWarehouseItemsFromShipmentItemIds(array $ids): void;

    public function returnWarehouseItemsFromTransferItemIds(array $ids): void;

    public function get(string $productId = null, string $warehouseId = null, string $receivedAt = null): ?Collection;

    public function updateByAcceptanceAndProduct(array $data, string $id, string $productId): void;

    public function findFirstShipmentItemByProductWarehouseAndDate(string $productId, string $warehouseId, string $dateFrom): ?object;

    public function getTotalQuantity(string $productId, string $warehouseId): int;

    public function findById(string $id): ?object;

    public function update(string $id, array $data): int;

    public function getByWarehouseAndProduct(string $warehouseId, string $productId, array $filters = []): Collection;

    public function getAvailableForReservation(array $filters = []): Collection;
}
