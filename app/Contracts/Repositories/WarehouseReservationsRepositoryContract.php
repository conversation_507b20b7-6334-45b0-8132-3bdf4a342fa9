<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;

interface WarehouseReservationsRepositoryContract
{
    public function insert(array $data): bool;
    
    public function update(string $id, array $data): int;
    
    public function delete(string $id): int;
    
    public function findById(string $id): ?object;
    
    public function getByDocument(string $documentType, string $documentId): Collection;
    
    public function getByWarehouse(string $warehouseId, array $filters = []): Collection;
    
    public function getByProduct(string $productId, array $filters = []): Collection;
    
    public function getActiveReservations(string $warehouseId, string $productId): Collection;
    
    public function getExpiredReservations(): Collection;
    
    public function getAvailableQuantity(string $warehouseId, string $productId, array $filters = []): int;
    
    public function getReservedQuantity(string $warehouseId, string $productId, array $filters = []): int;
    
    public function findBestReservationForUse(string $warehouseId, string $productId, int $quantity, array $filters = []): ?object;
}
