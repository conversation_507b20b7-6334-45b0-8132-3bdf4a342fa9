<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;

interface WarehouseReceiptOrderItemsRepositoryContract
{
    public function insert(array $data): bool;
    
    public function bulkInsert(array $items): bool;
    
    public function update(string $id, array $data): int;
    
    public function delete(string $id): int;
    
    public function getByReceiptOrder(string $receiptOrderId): Collection;
    
    public function getByProduct(string $productId, array $filters = []): Collection;
}
