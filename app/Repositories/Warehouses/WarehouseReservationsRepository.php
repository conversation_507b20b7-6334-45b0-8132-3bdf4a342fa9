<?php

namespace App\Repositories\Warehouses;

use App\Contracts\Repositories\WarehouseReservationsRepositoryContract;
use App\Traits\HasOrderedUuid;
use App\Traits\HasTimestamps;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class WarehouseReservationsRepository implements WarehouseReservationsRepositoryContract
{
    use HasOrderedUuid;
    use HasTimestamps;

    private const TABLE = 'warehouse_reservations';

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);
        
        if (!isset($data['id'])) {
            $data['id'] = $this->generateUuid();
        }

        return DB::table(self::TABLE)->insert($data);
    }

    public function update(string $id, array $data): int
    {
        $data = $this->setUpdatedAt($data);

        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update($data);
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    public function findById(string $id): ?object
    {
        return DB::table(self::TABLE . ' as wr')
            ->leftJoin('products as p', 'wr.product_id', '=', 'p.id')
            ->leftJoin('warehouses as w', 'wr.warehouse_id', '=', 'w.id')
            ->leftJoin('warehouse_items as wi', 'wr.warehouse_item_id', '=', 'wi.id')
            ->select([
                'wr.*',
                DB::raw("json_build_object('id', p.id, 'title', p.title, 'code', p.code) as product"),
                DB::raw("json_build_object('id', w.id, 'name', w.name) as warehouse"),
                DB::raw("json_build_object('id', wi.id, 'batch_number', wi.batch_number, 'quantity', wi.quantity) as warehouse_item")
            ])
            ->where('wr.id', $id)
            ->first();
    }

    public function getByDocument(string $documentType, string $documentId): Collection
    {
        return DB::table(self::TABLE . ' as wr')
            ->leftJoin('products as p', 'wr.product_id', '=', 'p.id')
            ->leftJoin('warehouses as w', 'wr.warehouse_id', '=', 'w.id')
            ->select([
                'wr.*',
                DB::raw("json_build_object('id', p.id, 'title', p.title, 'code', p.code) as product"),
                DB::raw("json_build_object('id', w.id, 'name', w.name) as warehouse")
            ])
            ->where('wr.document_type', $documentType)
            ->where('wr.document_id', $documentId)
            ->orderBy('wr.priority')
            ->orderBy('wr.reserved_at')
            ->get();
    }

    public function getByWarehouse(string $warehouseId, array $filters = []): Collection
    {
        $query = DB::table(self::TABLE . ' as wr')
            ->leftJoin('products as p', 'wr.product_id', '=', 'p.id')
            ->select([
                'wr.*',
                DB::raw("json_build_object('id', p.id, 'name', p.name, 'sku', p.sku) as product")
            ])
            ->where('wr.warehouse_id', $warehouseId);

        $this->applyFilters($query, $filters);

        return $query->orderBy('wr.priority')
                    ->orderBy('wr.reserved_at')
                    ->get();
    }

    public function getByProduct(string $productId, array $filters = []): Collection
    {
        $query = DB::table(self::TABLE . ' as wr')
            ->leftJoin('warehouses as w', 'wr.warehouse_id', '=', 'w.id')
            ->select([
                'wr.*',
                DB::raw("json_build_object('id', w.id, 'name', w.name) as warehouse")
            ])
            ->where('wr.product_id', $productId);

        $this->applyFilters($query, $filters);

        return $query->orderBy('wr.priority')
                    ->orderBy('wr.reserved_at')
                    ->get();
    }

    public function getActiveReservations(string $warehouseId, string $productId): Collection
    {
        return DB::table(self::TABLE)
            ->where('warehouse_id', $warehouseId)
            ->where('product_id', $productId)
            ->where('status', 'active')
            ->where(function ($query) {
                $query->whereNull('expires_at')
                      ->orWhere('expires_at', '>', Carbon::now());
            })
            ->orderBy('priority')
            ->orderBy('reserved_at')
            ->get();
    }

    public function getExpiredReservations(): Collection
    {
        return DB::table(self::TABLE)
            ->where('status', 'active')
            ->where('expires_at', '<', Carbon::now())
            ->get();
    }

    public function getAvailableQuantity(string $warehouseId, string $productId, array $filters = []): int
    {
        $query = DB::table(self::TABLE)
            ->where('warehouse_id', $warehouseId)
            ->where('product_id', $productId)
            ->whereIn('status', ['active', 'partial'])
            ->where(function ($query) {
                $query->whereNull('expires_at')
                      ->orWhere('expires_at', '>', Carbon::now());
            });

        $this->applyFilters($query, $filters);

        return $query->sum(DB::raw('reserved_quantity - used_quantity'));
    }

    public function getReservedQuantity(string $warehouseId, string $productId, array $filters = []): int
    {
        $query = DB::table(self::TABLE)
            ->where('warehouse_id', $warehouseId)
            ->where('product_id', $productId)
            ->whereIn('status', ['active', 'partial']);

        $this->applyFilters($query, $filters);

        return $query->sum('reserved_quantity');
    }

    public function findBestReservationForUse(string $warehouseId, string $productId, int $quantity, array $filters = []): ?object
    {
        $query = DB::table(self::TABLE)
            ->where('warehouse_id', $warehouseId)
            ->where('product_id', $productId)
            ->where('status', 'active')
            ->where(DB::raw('reserved_quantity - used_quantity'), '>=', $quantity)
            ->where(function ($query) {
                $query->whereNull('expires_at')
                      ->orWhere('expires_at', '>', Carbon::now());
            });

        $this->applyFilters($query, $filters);

        return $query->orderBy('priority')
                    ->orderBy('reserved_at')
                    ->first();
    }

    private function applyFilters($query, array $filters): void
    {
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['reservation_type'])) {
            $query->where('reservation_type', $filters['reservation_type']);
        }

        if (isset($filters['document_type'])) {
            $query->where('document_type', $filters['document_type']);
        }

        if (isset($filters['priority_min'])) {
            $query->where('priority', '>=', $filters['priority_min']);
        }

        if (isset($filters['priority_max'])) {
            $query->where('priority', '<=', $filters['priority_max']);
        }

        if (isset($filters['batch_number'])) {
            $query->where('batch_number', $filters['batch_number']);
        }

        if (isset($filters['expires_before'])) {
            $query->where('expires_at', '<', $filters['expires_before']);
        }

        if (isset($filters['reserved_after'])) {
            $query->where('reserved_at', '>=', $filters['reserved_after']);
        }
    }
}
