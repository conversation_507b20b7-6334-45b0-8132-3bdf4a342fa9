<?php

namespace App\Repositories\Warehouses;

use App\Contracts\Repositories\WarehouseIssueOrderItemsRepositoryContract;
use App\Traits\HasOrderedUuid;
use App\Traits\HasTimestamps;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class WarehouseIssueOrderItemsRepository implements WarehouseIssueOrderItemsRepositoryContract
{
    use HasOrderedUuid;
    use HasTimestamps;

    private const TABLE = 'warehouse_issue_order_items';

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);
        
        if (!isset($data['id'])) {
            $data['id'] = $this->generateUuid();
        }

        return DB::table(self::TABLE)->insert($data);
    }

    public function bulkInsert(array $items): bool
    {
        $preparedItems = [];
        
        foreach ($items as $item) {
            $item = $this->setTimestamps($item);
            
            if (!isset($item['id'])) {
                $item['id'] = $this->generateUuid();
            }
            
            $preparedItems[] = $item;
        }

        return DB::table(self::TABLE)->insert($preparedItems);
    }

    public function update(string $id, array $data): int
    {
        $data = $this->setUpdatedAt($data);

        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update($data);
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    public function getByIssueOrder(string $issueOrderId): Collection
    {
        return DB::table(self::TABLE)
            ->where('issue_order_id', $issueOrderId)
            ->orderBy('created_at')
            ->get();
    }

    public function getByProduct(string $productId, array $filters = []): Collection
    {
        $query = DB::table(self::TABLE)
            ->where('product_id', $productId);

        if (isset($filters['warehouse_id'])) {
            $query->join('warehouse_issue_orders', 'warehouse_issue_order_items.issue_order_id', '=', 'warehouse_issue_orders.id')
                  ->where('warehouse_issue_orders.warehouse_id', $filters['warehouse_id']);
        }

        if (isset($filters['date_from'])) {
            $query->join('warehouse_issue_orders', 'warehouse_issue_order_items.issue_order_id', '=', 'warehouse_issue_orders.id')
                  ->where('warehouse_issue_orders.date_from', '>=', $filters['date_from']);
        }

        return $query->get();
    }
}
