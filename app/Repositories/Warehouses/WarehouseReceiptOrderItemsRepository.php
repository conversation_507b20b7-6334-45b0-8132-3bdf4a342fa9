<?php

namespace App\Repositories\Warehouses;

use App\Contracts\Repositories\WarehouseReceiptOrderItemsRepositoryContract;
use App\Traits\HasOrderedUuid;
use App\Traits\HasTimestamps;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class WarehouseReceiptOrderItemsRepository implements WarehouseReceiptOrderItemsRepositoryContract
{
    use HasOrderedUuid;
    use HasTimestamps;

    private const TABLE = 'warehouse_receipt_order_items';

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);
        
        if (!isset($data['id'])) {
            $data['id'] = $this->generateUuid();
        }

        return DB::table(self::TABLE)->insert($data);
    }

    public function bulkInsert(array $items): bool
    {
        $preparedItems = [];
        
        foreach ($items as $item) {
            $item = $this->setTimestamps($item);
            
            if (!isset($item['id'])) {
                $item['id'] = $this->generateUuid();
            }
            
            $preparedItems[] = $item;
        }

        return DB::table(self::TABLE)->insert($preparedItems);
    }

    public function update(string $id, array $data): int
    {
        $data = $this->setUpdatedAt($data);

        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update($data);
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    public function getByReceiptOrder(string $receiptOrderId): Collection
    {
        return DB::table(self::TABLE)
            ->where('receipt_order_id', $receiptOrderId)
            ->orderBy('created_at')
            ->get();
    }

    public function getByProduct(string $productId, array $filters = []): Collection
    {
        $query = DB::table(self::TABLE)
            ->where('product_id', $productId);

        if (isset($filters['warehouse_id'])) {
            $query->join('warehouse_receipt_orders', 'warehouse_receipt_order_items.receipt_order_id', '=', 'warehouse_receipt_orders.id')
                  ->where('warehouse_receipt_orders.warehouse_id', $filters['warehouse_id']);
        }

        if (isset($filters['date_from'])) {
            $query->join('warehouse_receipt_orders', 'warehouse_receipt_order_items.receipt_order_id', '=', 'warehouse_receipt_orders.id')
                  ->where('warehouse_receipt_orders.date_from', '>=', $filters['date_from']);
        }

        return $query->get();
    }
}
