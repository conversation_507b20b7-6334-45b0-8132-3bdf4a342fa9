<?php

namespace App\Repositories\Warehouses;

use App\Contracts\Repositories\WarehouseOrderSchemesRepositoryContract;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class WarehouseOrderSchemesRepository implements WarehouseOrderSchemesRepositoryContract
{
    public function insert(array $data): void
    {
        DB::table('warehouse_order_schemes')
            ->insert(
                array_merge(
                    $data,
                    ['created_at' => Carbon::now()]
                )
            );
    }

    public function updateByWarehouseId(string $id, array $data): void
    {
        DB::table('warehouse_order_schemes')
            ->where('warehouse_id', $id)
            ->update(
                array_merge($data, ['updated_at' => Carbon::now()])
            );
    }

    public function findByWarehouseId(string $warehouseId): ?object
    {
        return DB::table('warehouse_order_schemes')
            ->where('warehouse_id', $warehouseId)
            ->first();
    }
}
