<?php

namespace App\Repositories\Warehouses;

use App\Contracts\Repositories\WarehouseTransactionsRepositoryContract;
use App\Traits\HasOrderedUuid;
use App\Traits\HasTimestamps;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class WarehouseTransactionsRepository implements WarehouseTransactionsRepositoryContract
{
    use HasOrderedUuid;
    use HasTimestamps;

    private const TABLE = 'warehouse_transactions';

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);
        
        if (!isset($data['id'])) {
            $data['id'] = $this->generateUuid();
        }

        return DB::table(self::TABLE)->insert($data);
    }

    public function bulkInsert(array $items): bool
    {
        $preparedItems = [];
        
        foreach ($items as $item) {
            $item = $this->setTimestamps($item);
            
            if (!isset($item['id'])) {
                $item['id'] = $this->generateUuid();
            }
            
            $preparedItems[] = $item;
        }

        return DB::table(self::TABLE)->insert($preparedItems);
    }

    public function update(string $id, array $data): int
    {
        $data = $this->setUpdatedAt($data);

        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update($data);
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    public function getByDocument(string $documentType, string $documentId): Collection
    {
        return DB::table(self::TABLE . ' as wt')
            ->leftJoin('products as p', 'wt.product_id', '=', 'p.id')
            ->leftJoin('warehouses as w', 'wt.warehouse_id', '=', 'w.id')
            ->select([
                'wt.*',
                DB::raw("json_build_object('id', p.id, 'name', p.name, 'sku', p.sku) as product"),
                DB::raw("json_build_object('id', w.id, 'name', w.name) as warehouse")
            ])
            ->where('wt.document_type', $documentType)
            ->where('wt.document_id', $documentId)
            ->orderBy('wt.created_at', 'desc')
            ->get();
    }

    public function getByWarehouse(string $warehouseId, array $filters = []): Collection
    {
        $query = DB::table(self::TABLE . ' as wt')
            ->leftJoin('products as p', 'wt.product_id', '=', 'p.id')
            ->select([
                'wt.*',
                DB::raw("json_build_object('id', p.id, 'name', p.name, 'sku', p.sku) as product")
            ])
            ->where('wt.warehouse_id', $warehouseId);

        if (isset($filters['date_from'])) {
            $query->where('wt.created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('wt.created_at', '<=', $filters['date_to']);
        }

        if (isset($filters['operation_type'])) {
            $query->where('wt.operation_type', $filters['operation_type']);
        }

        if (isset($filters['product_id'])) {
            $query->where('wt.product_id', $filters['product_id']);
        }

        return $query->orderBy('wt.created_at', 'desc')->get();
    }

    public function getByProduct(string $productId, array $filters = []): Collection
    {
        $query = DB::table(self::TABLE . ' as wt')
            ->leftJoin('warehouses as w', 'wt.warehouse_id', '=', 'w.id')
            ->select([
                'wt.*',
                DB::raw("json_build_object('id', w.id, 'name', w.name) as warehouse")
            ])
            ->where('wt.product_id', $productId);

        if (isset($filters['warehouse_id'])) {
            $query->where('wt.warehouse_id', $filters['warehouse_id']);
        }

        if (isset($filters['date_from'])) {
            $query->where('wt.created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('wt.created_at', '<=', $filters['date_to']);
        }

        if (isset($filters['operation_type'])) {
            $query->where('wt.operation_type', $filters['operation_type']);
        }

        return $query->orderBy('wt.created_at', 'desc')->get();
    }

    public function getMovementReport(string $warehouseId, string $dateFrom, string $dateTo): Collection
    {
        return DB::table(self::TABLE . ' as wt')
            ->leftJoin('products as p', 'wt.product_id', '=', 'p.id')
            ->select([
                'wt.product_id',
                'wt.operation_type',
                'wt.quantity',
                'wt.created_at',
                'p.name as product_name',
                'p.sku as product_sku'
            ])
            ->where('wt.warehouse_id', $warehouseId)
            ->whereBetween('wt.created_at', [$dateFrom, $dateTo])
            ->orderBy('wt.created_at', 'desc')
            ->get();
    }

    public function getByReservation(string $reservationId): Collection
    {
        return DB::table(self::TABLE)
            ->where('reservation_id', $reservationId)
            ->get();
    }

    public function getByWarehouseAndProduct(string $warehouseId, string $productId, array $filters = []): Collection
    {
        $query = DB::table(self::TABLE)
            ->where('warehouse_id', $warehouseId)
            ->where('product_id', $productId);

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        if (isset($filters['operation_type'])) {
            $query->where('operation_type', $filters['operation_type']);
        }

        if (isset($filters['quality_status'])) {
            $query->where('quality_status', $filters['quality_status']);
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    public function getTotalQuantityByOperation(string $warehouseId, string $productId, string $operationType, array $filters = []): int
    {
        $query = DB::table(self::TABLE)
            ->where('warehouse_id', $warehouseId)
            ->where('product_id', $productId)
            ->where('operation_type', $operationType);

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        return $query->sum('quantity');
    }
}
