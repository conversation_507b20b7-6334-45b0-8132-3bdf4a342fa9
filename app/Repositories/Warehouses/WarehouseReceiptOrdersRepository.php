<?php

namespace App\Repositories\Warehouses;

use App\Contracts\Repositories\WarehouseReceiptOrdersRepositoryContract;
use App\Traits\HasOrderedUuid;
use App\Traits\HasTimestamps;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class WarehouseReceiptOrdersRepository implements WarehouseReceiptOrdersRepositoryContract
{
    use HasOrderedUuid;
    use HasTimestamps;

    private const TABLE = 'warehouse_receipt_orders';

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);
        
        if (!isset($data['id'])) {
            $data['id'] = $this->generateUuid();
        }

        return DB::table(self::TABLE)->insert($data);
    }

    public function update(string $id, array $data): int
    {
        $data = $this->setUpdatedAt($data);

        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update($data);
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    public function findById(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }

    public function getByWarehouse(string $warehouseId, array $filters = []): Collection
    {
        $query = DB::table(self::TABLE)
            ->where('warehouse_id', $warehouseId);

        if (isset($filters['date_from'])) {
            $query->where('date_from', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('date_from', '<=', $filters['date_to']);
        }

        if (isset($filters['held'])) {
            $query->where('held', $filters['held']);
        }

        if (isset($filters['status_id'])) {
            $query->where('status_id', $filters['status_id']);
        }

        return $query->orderBy('date_from', 'desc')->get();
    }

    public function getByDocument(string $documentType, string $documentId): Collection
    {
        return DB::table(self::TABLE)
            ->where('document_basis_type', $documentType)
            ->where('document_basis_id', $documentId)
            ->get();
    }

    public function getByDateRange(string $warehouseId, string $dateFrom, string $dateTo): Collection
    {
        return DB::table(self::TABLE)
            ->where('warehouse_id', $warehouseId)
            ->whereBetween('date_from', [$dateFrom, $dateTo])
            ->orderBy('date_from', 'desc')
            ->get();
    }
}
