<?php

namespace App\Repositories\Warehouses;

use App\Contracts\Repositories\WarehouseItemsRepositoryContract;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class WarehouseItemsRepository implements WarehouseItemsRepositoryContract
{
    private const TABLE = 'warehouse_items';

    public function create(array $data): void
    {
        DB::table(self::TABLE)
            ->insert($data);
    }

    public function upsert(array $data, array $updateColumns = null): void
    {
        DB::table(self::TABLE)->upsert(
            $data,
            ['id'],
            $updateColumns
        );
    }

    public function deleteWhereInIds(array $ids): void
    {
        DB::table(self::TABLE)
            ->whereIn('id', $ids)
            ->delete();
    }

    public function returnWarehouseItemsFromShipmentItemIds(array $ids): void
    {
        DB::statement('
                WITH summed_quantities AS (
                    SELECT swi.warehouse_item_id, SUM(swi.quantity) as total_quantity
                    FROM shipment_warehouse_items swi
                    WHERE swi.shipment_item_id = ANY(ARRAY[' . implode(',', array_fill(0, count($ids), '?::uuid')) . '])
                    GROUP BY swi.warehouse_item_id
                )
                UPDATE warehouse_items wi
                SET quantity = wi.quantity + sq.total_quantity,
                    status = \'in_stock\'
                FROM summed_quantities sq
                WHERE wi.id = sq.warehouse_item_id;
            ', $ids);
    }

    public function returnWarehouseItemsFromTransferItemIds(array $ids): void
    {
        DB::statement('
                WITH summed_quantities AS (
                    SELECT gtwi.warehouse_item_id, SUM(gtwi.quantity) as total_quantity
                    FROM goods_transfer_warehouse_items gtwi
                    WHERE gtwi.goods_transfer_item_id = ANY(ARRAY[' . implode(',', array_fill(0, count($ids), '?::uuid')) . '])
                    GROUP BY gtwi.warehouse_item_id
                )
                UPDATE warehouse_items wi
                SET quantity = wi.quantity + sq.total_quantity,
                    status = \'in_stock\'
                FROM summed_quantities sq
                WHERE wi.id = sq.warehouse_item_id;
            ', $ids);
    }

    public function getTotalQuantity(string $productId, string $warehouseId): int
    {
        return (int) DB::table(self::TABLE)
            ->where('product_id', $productId)
            ->where('warehouse_id', $warehouseId)
            ->where('status', '<>', 'out_of_stock')
            ->sum('quantity');
    }

    public function get(string $productId = null, string $warehouseId = null, string $receivedAt = null): ?Collection
    {
        return DB::table('warehouse_items as wi')
            ->join('acceptances as a', 'wi.acceptance_id', '=', 'a.id')
            ->when($productId, fn ($query) => $query->where('wi.product_id', $productId))
            ->when($warehouseId, fn ($query) => $query->where('wi.warehouse_id', $warehouseId))
            ->when($receivedAt, fn ($query) => $query->where('wi.received_at', '<=', $receivedAt))
            ->where('a.held', true)
            ->where('a.deleted_at', null)
            ->orderBy('wi.received_at', 'asc')
            ->select('wi.*')
            ->get();
    }

    public function update(array $data, string $id, string $productId): void
    {
        DB::table(self::TABLE)
            ->where('acceptance_id', $id)
            ->where('product_id', $productId)
            ->update($data);
    }

    public function findFirstShipmentItemByProductWarehouseAndDate(
        string $productId,
        string $warehouseId,
        string $dateFrom
    ): ?object {
        return DB::table('warehouse_items as wi')
            ->join('shipment_warehouse_items as swi', 'swi.warehouse_item_id', '=', 'wi.id')
            ->where('wi.product_id', $productId)
            ->where('wi.warehouse_id', $warehouseId)
            ->where('wi.received_at', '>=', $dateFrom)
            ->orderBy('wi.received_at', 'asc')
            ->select('swi.shipment_item_id')
            ->first();
    }

    public function getShipmentItemIdByAcceptance(string $acceptanceId, string $productId): ?object
    {
        return DB::table('warehouse_items as wi')
            ->join('shipment_warehouse_items as swi', 'swi.warehouse_item_id', '=', 'wi.id')
            ->join('shipment_items as s', 's.id', '=', 'swi.shipment_item_id')
            ->join('shipments as sh', 'sh.id', '=', 's.shipment_id')
            ->where('wi.acceptance_id', $acceptanceId)
            ->where('wi.product_id', $productId)
            ->orderBy('sh.date_from', 'asc')
            ->select('s.id')
            ->first();
    }

    public function getShipmentItemIdByAcceptanceAndProductIds(string $acceptanceId, array $productIds): ?string
    {
        return DB::table('warehouse_items as wi')
            ->join('shipment_warehouse_items as swi', 'swi.warehouse_item_id', '=', 'wi.id')
            ->where('wi.acceptance_id', $acceptanceId)
            ->where('wi.product_id', $productIds)
            ->orderBy('swi.shipment_date')
            ->value('swi.shipment_item_id');
    }

    public function deleteWhereAcceptanceAndProductIds(string $acceptanceId, array $productId): void
    {
        DB::table('warehouse_items')
            ->where('acceptance_id', $acceptanceId)
            ->where('product_id', $productId)
            ->delete();
    }

    public function bulkUpdate(array $acceptanceIds, string $productId, array $data): int
    {
        return DB::table(self::TABLE)
            ->whereIn('acceptance_id', $acceptanceIds)
            ->where('product_id', $productId)
            ->update($data);
    }

    public function bulkInsert(array $items): bool
    {
        return DB::table(self::TABLE)->insert($items);
    }

    public function findById(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update($data);
    }

    public function getByWarehouseAndProduct(string $warehouseId, string $productId, array $filters = []): Collection
    {
        $query = DB::table(self::TABLE)
            ->where('warehouse_id', $warehouseId)
            ->where('product_id', $productId);

        if (isset($filters['quality_status'])) {
            $query->where('quality_status', $filters['quality_status']);
        }

        if (isset($filters['batch_number'])) {
            $query->where('batch_number', $filters['batch_number']);
        }

        if (isset($filters['expiry_after'])) {
            $query->where('expiry_date', '>', $filters['expiry_after']);
        }

        if (isset($filters['storage_location'])) {
            $query->where('storage_location', $filters['storage_location']);
        }

        return $query->orderBy('received_at')->get();
    }

    public function getAvailableForReservation(array $filters = []): Collection
    {
        $query = DB::table(self::TABLE)
            ->whereRaw('quantity > reserved_quantity')
            ->where('status', 'in_stock');

        if (isset($filters['warehouse_id'])) {
            $query->where('warehouse_id', $filters['warehouse_id']);
        }

        if (isset($filters['product_id'])) {
            $query->where('product_id', $filters['product_id']);
        }

        if (isset($filters['quality_status'])) {
            $query->where('quality_status', $filters['quality_status']);
        } else {
            $query->where('quality_status', 'good'); // По умолчанию только качественные товары
        }

        if (isset($filters['batch_number'])) {
            $query->where('batch_number', $filters['batch_number']);
        }

        if (isset($filters['expiry_after'])) {
            $query->where('expiry_date', '>', $filters['expiry_after']);
        }

        if (isset($filters['available_quantity_min'])) {
            $query->whereRaw('(quantity - reserved_quantity) >= ?', [$filters['available_quantity_min']]);
        }

        // Сортировка FIFO: сначала по сроку годности, потом по дате поступления
        return $query->orderBy('expiry_date', 'asc')
                    ->orderBy('received_at', 'asc')
                    ->get();
    }
}
