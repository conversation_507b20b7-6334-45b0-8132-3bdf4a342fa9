<?php

namespace App\Repositories\Contractors\Detail;

use App\Contracts\Repositories\ContractorDetailsRepositoryContract;
use App\Traits\HasTimestamps;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ContractorDetailsRepository implements ContractorDetailsRepositoryContract
{
    use HasTimestamps;
    private const TABLE = 'contractor_details';

    public function get(
        ?string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        return collect();
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);
        return DB::table(self::TABLE)
            ->insert($data);
    }

    public function update(string $id, array $data): int
    {
        return 0;
    }

    public function delete(string $id): int
    {
        return 0;
    }

    public function getDetailsByContractorId(string $id): ?object
    {
        return DB::table(self::TABLE)->where('contractor_id', $id)->first();
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)->where('id', $id)->first();
    }

    public function upsert(array $data): int
    {
        return DB::table(self::TABLE)
            ->upsert(
                $data,
                ['contractor_id'],
                [
                    'taxation_type',
                    'type',
                    'inn','kpp','ogrn','okpo',
                    'full_name','firstname','patronymic',
                    'lastname','ogrnip',
                    'certificate_number','certificate_date',
                    'updated_at',
                    'vat_rate_id',
                ]
            );
    }
}
