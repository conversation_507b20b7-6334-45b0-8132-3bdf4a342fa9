<?php

namespace App\Http\Requests\Api\Internal\WarehouseOrderScheme;

use App\DTO\WarehouseOrderScheme\WarehouseReceiptOrderStoreDTO;
use Illuminate\Foundation\Http\FormRequest;

class WarehouseReceiptOrderStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'cabinet_id' => ['required', 'uuid', 'exists:cabinets,id'],
            'employee_id' => ['required', 'uuid', 'exists:employees,id'],
            'department_id' => ['required', 'uuid', 'exists:departments,id'],
            'warehouse_id' => ['required', 'uuid', 'exists:warehouses,id'],
            'number' => ['nullable', 'string', 'max:255'],
            'date_from' => ['required', 'date'],
            'status_id' => ['nullable', 'uuid', 'exists:statuses,id'],
            'document_basis_type' => ['nullable', 'string', 'max:255'],
            'document_basis_id' => ['nullable', 'uuid'],
            'reason' => ['nullable', 'string'],
            'total_quantity' => ['required', 'integer', 'min:0'],
            'total_cost' => ['required', 'string'],
            'comment' => ['nullable', 'string'],
        ];
    }

    public function messages(): array
    {
        return [
            'cabinet_id.required' => 'Кабинет обязателен',
            'employee_id.required' => 'Сотрудник обязателен',
            'department_id.required' => 'Подразделение обязательно',
            'warehouse_id.required' => 'Склад обязателен',
            'date_from.required' => 'Дата документа обязательна',
            'total_quantity.required' => 'Общее количество обязательно',
            'total_cost.required' => 'Общая стоимость обязательна',
        ];
    }

    public function toDTO(): WarehouseReceiptOrderStoreDTO
    {
        return WarehouseReceiptOrderStoreDTO::fromArray($this->validated());
    }
}
