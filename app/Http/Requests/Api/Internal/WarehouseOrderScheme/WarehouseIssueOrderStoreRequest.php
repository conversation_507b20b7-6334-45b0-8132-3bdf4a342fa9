<?php

namespace App\Http\Requests\Api\Internal\WarehouseOrderScheme;

use App\DTO\WarehouseOrderScheme\WarehouseIssueOrderStoreDTO;
use Illuminate\Foundation\Http\FormRequest;

class WarehouseIssueOrderStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'cabinet_id' => ['required', 'uuid', 'exists:cabinets,id'],
            'employee_id' => ['required', 'uuid', 'exists:employees,id'],
            'department_id' => ['required', 'uuid', 'exists:departments,id'],
            'warehouse_id' => ['required', 'uuid', 'exists:warehouses,id'],
            'number' => ['nullable', 'string', 'max:255'],
            'date_from' => ['required', 'date'],
            'status_id' => ['nullable', 'uuid', 'exists:statuses,id'],
            'document_basis_type' => ['nullable', 'string', 'max:255'],
            'document_basis_id' => ['nullable', 'uuid'],
            'write_off_reason' => ['required', 'in:defective,expired,shortage,internal_use,return_to_supplier,damage,other'],
            'reason_description' => ['nullable', 'string'],
            'total_quantity' => ['required', 'integer', 'min:0'],
            'total_cost' => ['required', 'string'],
            'comment' => ['nullable', 'string'],
        ];
    }

    public function messages(): array
    {
        return [
            'cabinet_id.required' => 'Кабинет обязателен',
            'employee_id.required' => 'Сотрудник обязателен',
            'department_id.required' => 'Подразделение обязательно',
            'warehouse_id.required' => 'Склад обязателен',
            'date_from.required' => 'Дата документа обязательна',
            'write_off_reason.required' => 'Причина списания обязательна',
            'write_off_reason.in' => 'Недопустимая причина списания',
            'total_quantity.required' => 'Общее количество обязательно',
            'total_cost.required' => 'Общая стоимость обязательна',
        ];
    }

    public function toDTO(): WarehouseIssueOrderStoreDTO
    {
        return WarehouseIssueOrderStoreDTO::fromArray($this->validated());
    }
}
