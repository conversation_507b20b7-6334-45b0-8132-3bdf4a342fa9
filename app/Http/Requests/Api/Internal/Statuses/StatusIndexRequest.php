<?php

namespace App\Http\Requests\Api\Internal\Statuses;

use App\Contracts\Requests\ToDtoContract;
use App\DTO\IndexRequestDTO;
use App\Entities\StatusEntity;
use App\Enums\Api\Internal\StatusTypeEnum;
use App\Rules\AllowedFieldsRule;
use App\Rules\ValidSortFieldRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StatusIndexRequest extends FormRequest implements ToDtoContract
{
    public function __construct(
        private readonly StatusEntity $entity
    ) {
        parent::__construct();
    }

    public function authorize(): bool
    {
        return true;
    }


    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',
            'fields' => 'nullable|array',
            'fields.*' => ['string', new AllowedFieldsRule($this->entity)],
            'sortField' => ['nullable', 'string', new ValidSortFieldRule($this->entity, $this)],
            'filters.type.value' => ['string', Rule::in(StatusTypeEnum::cases())],
            'sortDirection' => 'nullable|string|in:asc,desc',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
            'filters.search.value' => 'string',
        ];
    }

    public function toDTO(): IndexRequestDTO
    {
        $data = $this->validated();
        return IndexRequestDTO::fromArray(
            array_merge(
                ['id' => $data['cabinet_id']],
                $data
            )
        );
    }
}
