<?php

namespace App\Http\Resources\Other\Status;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StatusIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор статуса */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function () {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function () {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->when(
                property_exists($this->resource, 'deleted_at'),
                function () {
                    return $this->resource->deleted_at;
                }
            ),
            /** @var string|null $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function () {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string $name Название статуса */
            'name' => $this->when(
                property_exists($this->resource, 'name'),
                function () {
                    return (string) $this->resource->name;
                }
            ),
            /** @var string $color Цвет статуса */
            'color' => $this->when(
                property_exists($this->resource, 'color'),
                function () {
                    return (string) $this->resource->color;
                }
            ),
            /** @var string $type_id Идентификатор типа статуса */
            'type' => $this->when(
                property_exists($this->resource, 'type'),
                function () {
                    return $this->resource->type;
                }
            ),
            /** @var int $sort Порядок сортировки */
            'sort' => $this->when(
                property_exists($this->resource, 'sort'),
                function () {
                    return (int) $this->resource->sort;
                }
            ),
        ];
    }
}
