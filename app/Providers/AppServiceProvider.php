<?php

namespace App\Providers;

use App\Contracts\Repositories\FileRelationsRepositoryContract;
use App\Contracts\Repositories\WarehouseCellGroupsRepositoryContract;
use App\Contracts\Repositories\WarehouseGroupsRepositoryContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Contracts\Services\Internal\ArchiveServiceContract;
use App\Contracts\Services\Internal\AttributeGroupsServiceContract;
use App\Contracts\Services\Internal\AttributesServiceContract;
use App\Contracts\Services\Internal\AttributeValuesServiceContract;
use App\Contracts\Services\Internal\BarcodesServiceContract;
use App\Contracts\Services\Internal\BinServiceContract;
use App\Contracts\Services\Internal\BookmarksServiceContract;
use App\Contracts\Services\Internal\BrandsServiceContract;
use App\Contracts\Services\Internal\Cabinet\CabinetInvitesServiceContract;
use App\Contracts\Services\Internal\Cabinet\CabinetSettingsServiceContract;
use App\Contracts\Services\Internal\Cabinet\CabinetsServiceContract;
use App\Contracts\Services\Internal\Cabinet\EmployeeServiceContract;
use App\Contracts\Services\Internal\Contractors\ContractorGroupsServiceContract;
use App\Contracts\Services\Internal\Contractors\ContractorsServiceContract;
use App\Contracts\Services\Internal\Contractors\ContractServiceContract;
use App\Contracts\Services\Internal\DadataClientInterface;
use App\Contracts\Services\Internal\DepartamentPermissionsServiceContract;
use App\Contracts\Services\Internal\DepartmentsServiceContract;
use App\Contracts\Services\Internal\Directories\CabinetCurrenciesServiceContract;
use App\Contracts\Services\Internal\Directories\CountriesServiceContract;
use App\Contracts\Services\Internal\Directories\DiscountsServiceContract;
use App\Contracts\Services\Internal\Directories\GlobalCurrenciesServiceContract;
use App\Contracts\Services\Internal\Directories\LegalEntitiesServiceContract;
use App\Contracts\Services\Internal\Directories\MeasurementUnitGroupServiceContract;
use App\Contracts\Services\Internal\Directories\MeasurementUnitServiceContract;
use App\Contracts\Services\Internal\Directories\ProfitTaxRatesServiceContract;
use App\Contracts\Services\Internal\Directories\SalesChannelsServiceContract;
use App\Contracts\Services\Internal\Directories\VatRatesServiceContract;
use App\Contracts\Services\Internal\DocumentTypeResolverContract;
use App\Contracts\Services\Internal\WarehouseOrderSchemeDetectionServiceContract;
use App\Contracts\Services\Internal\EmployeePermissionsServiceContract;
use App\Contracts\Services\Internal\ExcelImportServiceContract;
use App\Contracts\Services\Internal\FifoServiceContract;
use App\Contracts\Services\Internal\FilesServiceContract;
use App\Contracts\Services\Internal\Finances\CabinetPricesServiceContract;
use App\Contracts\Services\Internal\Finances\FinanceServiceContract;
use App\Contracts\Services\Internal\Finances\IncomingPaymentItemsServiceContract;
use App\Contracts\Services\Internal\Finances\IncomingPaymentsServiceContract;
use App\Contracts\Services\Internal\Finances\OutgoingPaymentItemsServiceContract;
use App\Contracts\Services\Internal\Finances\OutgoingPaymentsServiceContract;
use App\Contracts\Services\Internal\GoodsTransferFifoServiceContract;
use App\Contracts\Services\Internal\OzonCredentialsServiceContract;
use App\Contracts\Services\Internal\OzonV1ReturnsFboFbsListServiceContract;
use App\Contracts\Services\Internal\OzonV2ReturnsRfbsListServiceContract;
use App\Contracts\Services\Internal\OzonV3FinanceTransactionListServiceContract;
use App\Contracts\Services\Internal\OzonV3PostingFbsListServiceContract;
use App\Contracts\Services\Internal\OzonWarehousesServiceContract;
use App\Contracts\Services\Internal\PackingsServiceContract;
use App\Contracts\Services\Internal\PermissionsServiceContract;
use App\Contracts\Services\Internal\Products\GoodsTransferItemsServiceContract;
use App\Contracts\Services\Internal\Products\GoodsTransferServiceContract;
use App\Contracts\Services\Internal\Products\ProductAccountingFeaturesServiceContract;
use App\Contracts\Services\Internal\Products\ProductAttributesServiceContract;
use App\Contracts\Services\Internal\Products\ProductCategoriesServiceContract;
use App\Contracts\Services\Internal\Products\ProductEgaisCodeServiceContract;
use App\Contracts\Services\Internal\Products\ProductGroupsServiceContract;
use App\Contracts\Services\Internal\Products\ProductPackingServiceContract;
use App\Contracts\Services\Internal\Products\ProductPricesServiceContract;
use App\Contracts\Services\Internal\Products\ProductsServiceContract;
use App\Contracts\Services\Internal\Products\ProductThresholdsServiceContract;
use App\Contracts\Services\Internal\Purchases\AcceptanceItemsServiceContract;
use App\Contracts\Services\Internal\Purchases\AcceptancesServiceContract;
use App\Contracts\Services\Internal\Purchases\VendorOrderItemsServiceContract;
use App\Contracts\Services\Internal\Purchases\VendorOrderServiceContract;
use App\Contracts\Services\Internal\RelatedDocumentsServiceContract;
use App\Contracts\Services\Internal\RolesServiceContract;
use App\Contracts\Services\Internal\Sales\ComissionReportsServiceContract;
use App\Contracts\Services\Internal\Sales\CustomerOrderItemsServiceContract;
use App\Contracts\Services\Internal\Sales\CustomerOrdersServiceContract;
use App\Contracts\Services\Internal\Sales\IssuedComissionReportItemsServiceContract;
use App\Contracts\Services\Internal\Sales\IssuedComissionReportServiceContract;
use App\Contracts\Services\Internal\Sales\ReceivedComissionReportRealizedItemsServiceContract;
use App\Contracts\Services\Internal\Sales\ReceivedComissionReportReturnItemsServiceContract;
use App\Contracts\Services\Internal\Sales\ReceivedComissionReportServiceContract;
use App\Contracts\Services\Internal\Sales\ShipmentItemsServiceContract;
use App\Contracts\Services\Internal\Sales\ShipmentsServiceContract;
use App\Contracts\Services\Internal\StatusesServiceContract;
use App\Contracts\Services\Internal\SuggestsServiceContract;
use App\Contracts\Services\Internal\UserViewSettingsServiceContract;
use App\Contracts\Services\Internal\Warehouses\WarehouseAddressServiceContract;
use App\Contracts\Services\Internal\Warehouses\WarehouseCellGroupsServiceContract;
use App\Contracts\Services\Internal\Warehouses\WarehouseCellServiceContract;
use App\Contracts\Services\Internal\Warehouses\WarehouseCellSizesServiceContract;
use App\Contracts\Services\Internal\Warehouses\WarehouseGroupsServiceContract;
use App\Contracts\Services\Internal\Warehouses\WarehousePhonesServiceContract;
use App\Contracts\Services\Internal\Warehouses\WarehouseServiceContract;
use App\Contracts\Services\Internal\Warehouses\WarehouseStorageAreasServiceContract;
use App\Contracts\Services\Internal\Warehouses\WorkSchedulesServiceContract;
use App\Events\BarcodeDeletedEvent;
use App\Listeners\DeleteBarcodeListener;
use App\Repositories\Files\FileRelationsRepository;
use App\Repositories\Warehouses\WarehouseCellGroupsRepository;
use App\Repositories\Warehouses\WarehouseGroupsRepository;
use App\Services\Api\Internal\AuthorizationService;
use App\Services\Api\Internal\Contractors\ContractorGroupsService\ContractorGroupsService;
use App\Services\Api\Internal\Contractors\ContractorsService\ContractorService;
use App\Services\Api\Internal\ContractService\ContractService;
use App\Services\Api\Internal\Documents\RelatedDocumentsService\RelatedDocumentsService;
use App\Services\Api\Internal\DocumentTypeResolver;
use App\Services\Api\Internal\FifoService\FifoService;
use App\Services\Api\Internal\Files\FilesService\FilesService;
use App\Services\Api\Internal\Goods\Other\AttributeGroupsService\AttributeGroupsService;
use App\Services\Api\Internal\Goods\Other\AttributesService\AttributeService;
use App\Services\Api\Internal\Goods\Other\AttributeValuesService\AttributeValuesService;
use App\Services\Api\Internal\Goods\Other\BarcodesService\BarcodesService;
use App\Services\Api\Internal\Goods\Other\BrandsService\BrandsService;
use App\Services\Api\Internal\Goods\Products\ProductAccountingFeaturesService\ProductAccountingFeaturesService;
use App\Services\Api\Internal\Goods\Products\ProductAttributesService\ProductAttributesService;
use App\Services\Api\Internal\Goods\Products\ProductCategoriesService\ProductCategoriesService;
use App\Services\Api\Internal\Goods\Products\ProductEgaisCodeService\ProductEgaisCodeService;
use App\Services\Api\Internal\Goods\Products\ProductGroupsService\ProductGroupsService;
use App\Services\Api\Internal\Goods\Products\ProductPackingService\ProductPackingService;
use App\Services\Api\Internal\Goods\Products\ProductPricesService\ProductPricesService;
use App\Services\Api\Internal\Goods\Products\ProductsService\ProductsService;
use App\Services\Api\Internal\Goods\Products\ProductThresholdsService\ProductThresholdsService;
use App\Services\Api\Internal\GoodsTransferFifoService\GoodsTransferFifoService;
use App\Services\Api\Internal\GoodsTransferItemService\GoodTransferItemsService;
use App\Services\Api\Internal\GoodsTransferService\GoodsTransferService;
use App\Services\Api\Internal\Money\IncomingPayments\IncomingPaymentItemsService\IncomingPaymentItemsService;
use App\Services\Api\Internal\Money\IncomingPayments\IncomingPaymentsService\IncomingPaymentsService;
use App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentItemsService\OutgoingPaymentItemsService;
use App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentsService\OutgoingPaymentsService;
use App\Services\Api\Internal\Money\PaymentsService\FinanceService;
use App\Services\Api\Internal\Other\ArchiveService\ArchiveService;
use App\Services\Api\Internal\Other\BinService\BinService;
use App\Services\Api\Internal\Other\ExcelImportService\ExcelImportService;
use App\Services\Api\Internal\Other\SuggestsService\SuggestsService;
use App\Services\Api\Internal\Ozon\OzonService\OzonCredentialsService\OzonCredentialsService;
use App\Services\Api\Internal\Ozon\OzonService\OzonV1ReturnsFboFbsListService\OzonV1ReturnsFboFbsListService;
use App\Services\Api\Internal\Ozon\OzonService\OzonV2ReturnsRfbsListService\OzonV2ReturnsRfbsListService;
use App\Services\Api\Internal\Ozon\OzonService\OzonV3FinanceTransactionListService\OzonV3FinanceTransactionListService;
use App\Services\Api\Internal\Ozon\OzonService\OzonV3PostingFbsListService\OzonV3PostingFbsListService;
use App\Services\Api\Internal\Ozon\OzonService\OzonWarehousesService\OzonWarehousesService;
use App\Services\Api\Internal\Procurement\AcceptanceItemsService\AcceptanceItemsService;
use App\Services\Api\Internal\Procurement\Acceptances\AcceptancesService\AcceptancesService;
use App\Services\Api\Internal\Procurement\VendorOrders\VendorOrderItemsService\VendorOrderItemsService;
use App\Services\Api\Internal\Procurement\VendorOrders\VendorOrdersService\VendorOrdersService;
use App\Services\Api\Internal\References\CabinetCurrenciesService\CabinetCurrencyService;
use App\Services\Api\Internal\References\CabinetPricesService\CabinetPricesService;
use App\Services\Api\Internal\References\CountriesService\CountryService;
use App\Services\Api\Internal\References\DiscountsService\DiscountsService;
use App\Services\Api\Internal\References\GlobalCurrenciesService\CurrencyServiceGlobal;
use App\Services\Api\Internal\References\LegalEntitiesService\LegalEntitiesService;
use App\Services\Api\Internal\References\MeasurementUnits\MeasurementGroupsService\MeasurementUnitGroupService;
use App\Services\Api\Internal\References\MeasurementUnits\MeasurementUnitsService\MeasurementUnitService;
use App\Services\Api\Internal\References\PackingsService\PackingsService;
use App\Services\Api\Internal\References\ProfitTaxRatesService\ProfitTaxRatesService;
use App\Services\Api\Internal\References\SalesChannelsService\SalesCnannelsService;
use App\Services\Api\Internal\References\VatRatesService\VatRatesService;
use App\Services\Api\Internal\Sales\ComissionReports\ComissionReportsService;
use App\Services\Api\Internal\Sales\ComissionReports\IssuedReports\IssuedComissionReportsService;
use App\Services\Api\Internal\Sales\ComissionReports\IssuedReports\Items\IssuedComissionReportsItemsService;
use App\Services\Api\Internal\Sales\ComissionReports\ReceivedReports\Items\ReceivedComissionReportsRealizedItemsService;
use App\Services\Api\Internal\Sales\ComissionReports\ReceivedReports\Items\ReceivedComissionReportsReturnItemsService;
use App\Services\Api\Internal\Sales\ComissionReports\ReceivedReports\ReceivedComissionReportsService;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrderItemsService\CustomerOrderItemsService;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\CustomerOrdersService;
use App\Services\Api\Internal\Sales\Shipments\ShipmentItemsService\ShipmentItemsService;
use App\Services\Api\Internal\Sales\Shipments\ShipmentsService\ShipmentsService;
use App\Services\Api\Internal\User\UserViewSettingsService\UserViewSettingsService;
use App\Services\Api\Internal\Warehouses\WarehouseAddressService\WarehouseAddressService;
use App\Services\Api\Internal\Warehouses\WarehouseCellGroupsService\WarehouseCellGroupService;
use App\Services\Api\Internal\Warehouses\WarehouseCellSizesService\WarehouseCellSizeService;
use App\Services\Api\Internal\Warehouses\WarehouseCellsService\WarehouseCellService;
use App\Services\Api\Internal\Warehouses\WarehouseGroups\WarehouseGroupService;
use App\Services\Api\Internal\Warehouses\WarehousePhonesService\WarehousePhoneService;
use App\Services\Api\Internal\Warehouses\WarehouseService\WarehouseService;
use App\Services\Api\Internal\Warehouses\WarehouseStorageAreasService\WarehouseStorageAreaService;
use App\Services\Api\Internal\Warehouses\WarehouseWorkSchedulesService\WorkScheduleService;
use App\Services\Api\Internal\WarehouseOrderScheme\WarehouseOrderSchemeDetectionService;
use App\Services\Api\Internal\WarehouseTransactionService\WarehouseTransactionService;
use App\Services\Api\Internal\Workspace\BookmarksService\BookmarksService;
use App\Services\Api\Internal\Workspace\CabinetInviteService\CabinetInviteService;
use App\Services\Api\Internal\Workspace\CabinetSettingsService\CabinetSettingsService;
use App\Services\Api\Internal\Workspace\CabinetsService\CabinetsService;
use App\Services\Api\Internal\Workspace\DepartmentsService\DepartmentsService;
use App\Services\Api\Internal\Workspace\EmployeesService\EmployeesService;
use App\Services\Api\Internal\Workspace\Permissions\DepartmentPermissionsService\DepartmentPermissionsService;
use App\Services\Api\Internal\Workspace\Permissions\EmployeePermissionsService\EmployeePermissionsService;
use App\Services\Api\Internal\Workspace\Permissions\PermissionsService\PermissionsService;
use App\Services\Api\Internal\Workspace\Permissions\RolesService\RolesService;
use App\Services\Api\Internal\Workspace\StatusesService\StatusesService;
use App\Services\Internal\DadataClientAdapter;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind(AcceptancesServiceContract::class, function () {
            //$request = $this->app->make(Request::class);

            // Определяем, какая версия API используется
            /*if ($request->is('api/v1/*')) {
                return $app->make(AcceptanceServiceV2::class);
            }*/

            // По умолчанию используем реализацию для internal
            return $this->app->make(AcceptancesService::class);
        });
        $this->app->bind(AcceptanceItemsServiceContract::class, AcceptanceItemsService::class);

        $this->app->bind(CustomerOrdersServiceContract::class, CustomerOrdersService::class);
        $this->app->bind(CustomerOrderItemsServiceContract::class, CustomerOrderItemsService::class);

        $this->app->bind(ShipmentsServiceContract::class, ShipmentsService::class);
        $this->app->bind(ShipmentItemsServiceContract::class, ShipmentItemsService::class);

        $this->app->bind(VendorOrderServiceContract::class, VendorOrdersService::class);
        $this->app->bind(VendorOrderItemsServiceContract::class, VendorOrderItemsService::class);

        $this->app->bind(FifoServiceContract::class, FifoService::class);
        $this->app->bind(CabinetInvitesServiceContract::class, CabinetInviteService::class);
        $this->app->bind(CountriesServiceContract::class, CountryService::class);
        $this->app->bind(GlobalCurrenciesServiceContract::class, CurrencyServiceGlobal::class);

        $this->app->bind(SalesChannelsServiceContract::class, SalesCnannelsService::class);
        $this->app->bind(VatRatesServiceContract::class, VatRatesService::class);
        $this->app->bind(WorkSchedulesServiceContract::class, WorkScheduleService::class);
        $this->app->bind(WarehouseServiceContract::class, WarehouseService::class);
        $this->app->bind(WarehouseCellServiceContract::class, WarehouseCellService::class);
        $this->app->bind(WarehouseCellSizesServiceContract::class, WarehouseCellSizeService::class);
        $this->app->bind(WarehouseCellGroupsServiceContract::class, WarehouseCellGroupService::class);
        $this->app->bind(WarehouseGroupsServiceContract::class, WarehouseGroupService::class);
        $this->app->bind(WarehouseStorageAreasServiceContract::class, WarehouseStorageAreaService::class);
        $this->app->bind(WarehouseAddressServiceContract::class, WarehouseAddressService::class);

        // Repositories
        $this->app->bind(WarehouseGroupsRepositoryContract::class, WarehouseGroupsRepository::class);
        $this->app->bind(WarehouseCellGroupsRepositoryContract::class, WarehouseCellGroupsRepository::class);

        //Платежи
        $this->app->bind(IncomingPaymentsServiceContract::class, IncomingPaymentsService::class);
        $this->app->bind(IncomingPaymentItemsServiceContract::class, IncomingPaymentItemsService::class);
        $this->app->bind(OutgoingPaymentsServiceContract::class, OutgoingPaymentsService::class);
        $this->app->bind(OutgoingPaymentItemsServiceContract::class, OutgoingPaymentItemsService::class);
        $this->app->bind(RolesServiceContract::class, RolesService::class);
        $this->app->bind(EmployeePermissionsServiceContract::class, EmployeePermissionsService::class);
        $this->app->bind(DepartamentPermissionsServiceContract::class, DepartmentPermissionsService::class);
        $this->app->bind(EmployeeServiceContract::class, EmployeesService::class);
        $this->app->bind(PermissionsServiceContract::class, PermissionsService::class);
        $this->app->bind(RelatedDocumentsServiceContract::class, RelatedDocumentsService::class);

        $this->app->bind(
            DocumentTypeResolverContract::class,
            DocumentTypeResolver::class
        );
        $this->app->bind(StatusesServiceContract::class, StatusesService::class);
        $this->app->bind(FilesServiceContract::class, FilesService::class);
        $this->app->bind(CabinetsServiceContract::class, CabinetsService::class);
        $this->app->bind(AttributesServiceContract::class, AttributeService::class);
        $this->app->bind(AttributeGroupsServiceContract::class, AttributeGroupsService::class);
        $this->app->bind(BrandsServiceContract::class, BrandsService::class);
        $this->app->bind(CabinetPricesServiceContract::class, CabinetPricesService::class);
        $this->app->bind(CabinetSettingsServiceContract::class, CabinetSettingsService::class);
        $this->app->bind(ProductCategoriesServiceContract::class, ProductCategoriesService::class);
        $this->app->bind(ContractorsServiceContract::class, ContractorService::class);
        $this->app->bind(WarehouseOrderSchemeDetectionServiceContract::class, WarehouseOrderSchemeDetectionService::class);
        $this->app->bind(WarehouseTransactionServiceContract::class, WarehouseTransactionService::class);
        $this->app->bind(WarehouseReservationServiceContract::class, WarehouseReservationService::class);
        $this->app->bind(DepartmentsServiceContract::class, DepartmentsService::class);
        $this->app->bind(DiscountsServiceContract::class, DiscountsService::class);
        $this->app->bind(LegalEntitiesServiceContract::class, LegalEntitiesService::class);
        $this->app->bind(PackingsServiceContract::class, PackingsService::class);
        $this->app->singleton(AuthorizationServiceContract::class, AuthorizationService::class);
        $this->app->bind(FinanceServiceContract::class, FinanceService::class);
        $this->app->bind(MeasurementUnitServiceContract::class, MeasurementUnitService::class);
        $this->app->bind(SuggestsServiceContract::class, SuggestsService::class);
        $this->app->bind(ProfitTaxRatesServiceContract::class, ProfitTaxRatesService::class);
        $this->app->bind(ProductsServiceContract::class, ProductsService::class);
        $this->app->bind(BarcodesServiceContract::class, BarcodesService::class);
        $this->app->bind(ProductPricesServiceContract::class, ProductPricesService::class);
        $this->app->bind(ProductAccountingFeaturesServiceContract::class, ProductAccountingFeaturesService::class);
        $this->app->bind(ProductThresholdsServiceContract::class, ProductThresholdsService::class);
        $this->app->bind(ProductEgaisCodeServiceContract::class, ProductEgaisCodeService::class);
        $this->app->bind(ProductAttributesServiceContract::class, ProductAttributesService::class);
        $this->app->bind(AttributeValuesServiceContract::class, AttributeValuesService::class);

        $this->app->bind(OzonCredentialsServiceContract::class, OzonCredentialsService::class);
        $this->app->bind(OzonV3FinanceTransactionListServiceContract::class, OzonV3FinanceTransactionListService::class);
        $this->app->bind(OzonV1ReturnsFboFbsListServiceContract::class, OzonV1ReturnsFboFbsListService::class);
        $this->app->bind(OzonWarehousesServiceContract::class, OzonWarehousesService::class);
        $this->app->bind(FileRelationsRepositoryContract::class, FileRelationsRepository::class);
        $this->app->bind(OzonV2ReturnsRfbsListServiceContract::class, OzonV2ReturnsRfbsListService::class);
        $this->app->bind(ArchiveServiceContract::class, ArchiveService::class);
        $this->app->bind(BinServiceContract::class, BinService::class);
        $this->app->bind(UserViewSettingsServiceContract::class, UserViewSettingsService::class);
        $this->app->bind(CabinetCurrenciesServiceContract::class, CabinetCurrencyService::class);
        $this->app->bind(ExcelImportServiceContract::class, ExcelImportService::class);
        $this->app->bind(ProductPackingServiceContract::class, ProductPackingService::class);
        $this->app->bind(OzonV3PostingFbsListServiceContract::class, OzonV3PostingFbsListService::class);
        $this->app->bind(WarehousePhonesServiceContract::class, WarehousePhoneService::class);
        $this->app->bind(MeasurementUnitGroupServiceContract::class, MeasurementUnitGroupService::class);
        $this->app->bind(DadataClientInterface::class, function ($app) {
            return new DadataClientAdapter(
                config('dadata.key'),
                config('dadata.secret')
            );
        });
        $this->app->bind(ContractorGroupsServiceContract::class, ContractorGroupsService::class);
        $this->app->bind(ProductGroupsServiceContract::class, ProductGroupsService::class);
        $this->app->bind(BookmarksServiceContract::class, BookmarksService::class);
        $this->app->bind(ContractServiceContract::class, ContractService::class);
        $this->app->bind(GoodsTransferFifoServiceContract::class, GoodsTransferFifoService::class);
        $this->app->bind(GoodsTransferItemsServiceContract::class, GoodTransferItemsService::class);
        $this->app->bind(GoodsTransferServiceContract::class, GoodsTransferService::class);

        $this->app->bind(IssuedComissionReportServiceContract::class, IssuedComissionReportsService::class);
        $this->app->bind(ReceivedComissionReportServiceContract::class, ReceivedComissionReportsService::class);
        $this->app->bind(ComissionReportsServiceContract::class, ComissionReportsService::class);

        $this->app->bind(ReceivedComissionReportRealizedItemsServiceContract::class, ReceivedComissionReportsRealizedItemsService::class);
        $this->app->bind(ReceivedComissionReportReturnItemsServiceContract::class, ReceivedComissionReportsReturnItemsService::class);
        $this->app->bind(IssuedComissionReportItemsServiceContract::class, IssuedComissionReportsItemsService::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Event::listen(BarcodeDeletedEvent::class, DeleteBarcodeListener::class);
    }
}
