<?php

namespace App\Providers;

use App\Contracts\Policies\AttributeGroupPolicyContract;
use App\Contracts\Policies\AttributePolicyContract;
use App\Contracts\Policies\AttributeValuePolicyContract;
use App\Contracts\Policies\BarcodePolicyContract;
use App\Contracts\Policies\BinPolicyContract;
use App\Contracts\Policies\BookmarksPolicyContract;
use App\Contracts\Policies\Cabinet\CabinetInvitePolicyContract;
use App\Contracts\Policies\Cabinet\CabinetPolicyContract;
use App\Contracts\Policies\Cabinet\CabinetPricePolicyContract;
use App\Contracts\Policies\Cabinet\CabinetSettingsPolicyContract;
use App\Contracts\Policies\Cabinet\DepartmentPolicyContract;
use App\Contracts\Policies\Cabinet\EmployeePolicyContract;
use App\Contracts\Policies\Contractors\ContractorGroupsPolicyContract;
use App\Contracts\Policies\Contractors\ContractorPolicyContract;
use App\Contracts\Policies\Contractors\ContractPolicyContract;
use App\Contracts\Policies\Directories\CabinetCurrenciesPolicyContract;
use App\Contracts\Policies\Directories\CountryPolicyContract;
use App\Contracts\Policies\Directories\DiscountPolicyContract;
use App\Contracts\Policies\Directories\LegalEntityPolicyContract;
use App\Contracts\Policies\Directories\MeasurementUnitGroupPolicyContract;
use App\Contracts\Policies\Directories\MeasurementUnitPolicyContract;
use App\Contracts\Policies\Directories\Permissions\DepartmentPermissionPolicyContract;
use App\Contracts\Policies\Directories\Permissions\EmployeePermissionsPolicyContract;
use App\Contracts\Policies\Directories\Permissions\RolePolicyContract;
use App\Contracts\Policies\Directories\ProfitTaxRatePolicyContract;
use App\Contracts\Policies\Directories\SalesChannelPolicyContract;
use App\Contracts\Policies\Directories\StatusPolicyContract;
use App\Contracts\Policies\Directories\VatRatePolicyContract;
use App\Contracts\Policies\Directories\Warehouses\WarehouseAddressPolicyContract;
use App\Contracts\Policies\Directories\Warehouses\WarehouseCalendarPolicyContract;
use App\Contracts\Policies\Directories\Warehouses\WarehouseCellGroupPolicyContract;
use App\Contracts\Policies\Directories\Warehouses\WarehouseCellPolicyContract;
use App\Contracts\Policies\Directories\Warehouses\WarehouseCellSizePolicyContract;
use App\Contracts\Policies\Directories\Warehouses\WarehouseGroupsPolicyContract;
use App\Contracts\Policies\Directories\Warehouses\WarehousePhonePolicyContract;
use App\Contracts\Policies\Directories\Warehouses\WarehousePolicyContract;
use App\Contracts\Policies\Directories\Warehouses\WarehouseStorageAreaPolicyContract;
use App\Contracts\Policies\Directories\Warehouses\WarehouseWorkSchedulePolicyContract;
use App\Contracts\Policies\ExcelImportPolicyContract;
use App\Contracts\Policies\FilePolicyContract;
use App\Contracts\Policies\Finances\FinancePolicyContract;
use App\Contracts\Policies\Finances\IncomingPaymentItemPolicyContract;
use App\Contracts\Policies\Finances\IncomingPaymentPolicyContract;
use App\Contracts\Policies\Finances\OutgoingPaymentItemPolicyContract;
use App\Contracts\Policies\Finances\OutgoingPaymentPolicyContract;
use App\Contracts\Policies\OzonCredentialsPolicyContract;
use App\Contracts\Policies\OzonV1ReturnsFboFbsListPolicyContract;
use App\Contracts\Policies\OzonV2ReturnsRfbsListPolicyContract;
use App\Contracts\Policies\OzonV3FinanceTransactionListPolicyContract;
use App\Contracts\Policies\OzonV3PostingFbsListPolicyContract;
use App\Contracts\Policies\OzonWarehousesPolicyContract;
use App\Contracts\Policies\PackingPolicyContract;
use App\Contracts\Policies\Products\BrandPolicyContract;
use App\Contracts\Policies\Products\GoodsTransferItemPolicyContract;
use App\Contracts\Policies\Products\GoodsTransferPolicyContract;
use App\Contracts\Policies\Products\ProductAttributePolicyContract;
use App\Contracts\Policies\Products\ProductCategoryPolicyContract;
use App\Contracts\Policies\Products\ProductGroupsPolicyContract;
use App\Contracts\Policies\Products\ProductPackingPolicyContract;
use App\Contracts\Policies\Products\ProductPolicyContract;
use App\Contracts\Policies\Purchases\AcceptanceItemPolicyContract;
use App\Contracts\Policies\Purchases\AcceptancePolicyContract;
use App\Contracts\Policies\Purchases\VendorOrderItemPolicyContract;
use App\Contracts\Policies\Purchases\VendorOrderPolicyContract;
use App\Contracts\Policies\RelatedDocumentPolicyContract;
use App\Contracts\Policies\Sales\ComissionReportsPolicyContract;
use App\Contracts\Policies\Sales\CustomerOrderItemPolicyContract;
use App\Contracts\Policies\Sales\CustomerOrderPolicyContract;
use App\Contracts\Policies\Sales\IssuedComissionReportsItemPolicyContract;
use App\Contracts\Policies\Sales\IssuedComissionReportsPolicyContract;
use App\Contracts\Policies\Sales\ReceivedComissionReportsPolicyContract;
use App\Contracts\Policies\Sales\ReceivedComissionReportsRealizedItemPolicyContract;
use App\Contracts\Policies\Sales\ReceivedComissionReportsReturnItemPolicyContract;
use App\Contracts\Policies\Sales\ShipmentItemPolicyContract;
use App\Contracts\Policies\Sales\ShipmentPolicyContract;
use App\Contracts\Policies\UserViewSettingsPolicyContract;
use App\Policies\AcceptanceItemPolicy;
use App\Policies\AcceptancePolicy;
use App\Policies\AttributeGroupPolicy;
use App\Policies\AttributePolicy;
use App\Policies\AttributeValuePolicy;
use App\Policies\BarcodePolicy;
use App\Policies\BinPolicy;
use App\Policies\BookmarkPolicy;
use App\Policies\BrandPolicy;
use App\Policies\CabinetCurrenciesPolicy;
use App\Policies\CabinetInvitePolicy;
use App\Policies\CabinetPolicy;
use App\Policies\CabinetPricePolicy;
use App\Policies\CabinetSettingsPolicy;
use App\Policies\ComissionReportsPolicy;
use App\Policies\ContractorGroupsPolicy;
use App\Policies\ContractorPolicy;
use App\Policies\ContractPolicy;
use App\Policies\CountryPolicy;
use App\Policies\CustomerOrderItemPolicy;
use App\Policies\CustomerOrderPolicy;
use App\Policies\DepartmentPermissionPolicy;
use App\Policies\DepartmentPolicy;
use App\Policies\DiscountPolicy;
use App\Policies\EmployeePermissionsPolicy;
use App\Policies\EmployeePolicy;
use App\Policies\ExcelImportPolicy;
use App\Policies\FilePolicy;
use App\Policies\FinancePolicy;
use App\Policies\GoodsTransferItemPolicy;
use App\Policies\GoodsTransferPolicy;
use App\Policies\IncomingPaymentItemPolicy;
use App\Policies\IncomingPaymentPolicy;
use App\Policies\IssuedComissionReportItemPolicy;
use App\Policies\IssuedComissionReportsPolicy;
use App\Policies\LegalEntityPolicy;
use App\Policies\MeasurementUnitGroupPolicy;
use App\Policies\MeasurementUnitPolicy;
use App\Policies\OutgoingPaymentItemPolicy;
use App\Policies\OutgoingPaymentPolicy;
use App\Policies\OzonCredentialsPolicy;
use App\Policies\OzonV1ReturnsFboFbsListPolicy;
use App\Policies\OzonV2ReturnsRfbsListPolicy;
use App\Policies\OzonV3FinanceTransactionListPolicy;
use App\Policies\OzonV3PostingFbsListPolicy;
use App\Policies\OzonWarehousesPolicy;
use App\Policies\PackingPolicy;
use App\Policies\ProductAttributePolicy;
use App\Policies\ProductCategoryPolicy;
use App\Policies\ProductGroupsPolicy;
use App\Policies\ProductPackingPolicy;
use App\Policies\ProductPolicy;
use App\Policies\ProfitTaxRatePolicy;
use App\Policies\ReceivedComissionReportRealizedItemPolicy;
use App\Policies\ReceivedComissionReportReturnItemPolicy;
use App\Policies\ReceivedComissionReportsPolicy;
use App\Policies\RelatedDocumentPolicy;
use App\Policies\RolePolicy;
use App\Policies\SalesChannelPolicy;
use App\Policies\ShipmentItemPolicy;
use App\Policies\ShipmentPolicy;
use App\Policies\StatusPolicy;
use App\Policies\UserViewSettingsPolicy;
use App\Policies\VatRatePolicy;
use App\Policies\VendorOrderItemPolicy;
use App\Policies\VendorOrderPolicy;
use App\Policies\WarehouseAddressPolicy;
use App\Policies\WarehouseCalendarPolicy;
use App\Policies\WarehouseCellGroupPolicy;
use App\Policies\WarehouseCellPolicy;
use App\Policies\WarehouseCellSizePolicy;
use App\Policies\WarehouseGroupsPolicy;
use App\Policies\WarehousePhonePolicy;
use App\Policies\WarehousePolicy;
use App\Policies\WarehouseStorageAreaPolicy;
use App\Policies\WarehouseWorkSchedulePolicy;
use Illuminate\Support\ServiceProvider;

class PolicyProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(AcceptancePolicyContract::class, AcceptancePolicy::class);
        $this->app->singleton(AcceptanceItemPolicyContract::class, AcceptanceItemPolicy::class);
        $this->app->singleton(AttributePolicyContract::class, AttributePolicy::class);
        $this->app->singleton(AttributeGroupPolicyContract::class, AttributeGroupPolicy::class);
        $this->app->singleton(BrandPolicyContract::class, BrandPolicy::class);
        $this->app->singleton(CabinetPolicyContract::class, CabinetPolicy::class);
        $this->app->singleton(CabinetInvitePolicyContract::class, CabinetInvitePolicy::class);
        $this->app->singleton(CabinetPricePolicyContract::class, CabinetPricePolicy::class);
        $this->app->singleton(CabinetSettingsPolicyContract::class, CabinetSettingsPolicy::class);
        $this->app->singleton(ContractorPolicyContract::class, ContractorPolicy::class);
        $this->app->singleton(CountryPolicyContract::class, CountryPolicy::class);
        $this->app->singleton(CustomerOrderPolicyContract::class, CustomerOrderPolicy::class);
        $this->app->singleton(CustomerOrderItemPolicyContract::class, CustomerOrderItemPolicy::class);
        $this->app->singleton(DepartmentPolicyContract::class, DepartmentPolicy::class);
        $this->app->singleton(DiscountPolicyContract::class, DiscountPolicy::class);
        $this->app->singleton(EmployeePolicyContract::class, EmployeePolicy::class);
        $this->app->singleton(EmployeePermissionsPolicyContract::class, EmployeePermissionsPolicy::class);
        $this->app->singleton(FilePolicyContract::class, FilePolicy::class);
        $this->app->singleton(IncomingPaymentPolicyContract::class, IncomingPaymentPolicy::class);
        $this->app->singleton(IncomingPaymentItemPolicyContract::class, IncomingPaymentItemPolicy::class);
        $this->app->singleton(LegalEntityPolicyContract::class, LegalEntityPolicy::class);
        $this->app->singleton(MeasurementUnitPolicyContract::class, MeasurementUnitPolicy::class);
        $this->app->singleton(OutgoingPaymentPolicyContract::class, OutgoingPaymentPolicy::class);
        $this->app->singleton(OutgoingPaymentItemPolicyContract::class, OutgoingPaymentItemPolicy::class);
        $this->app->singleton(ProductCategoryPolicyContract::class, ProductCategoryPolicy::class);
        $this->app->singleton(RelatedDocumentPolicyContract::class, RelatedDocumentPolicy::class);
        $this->app->singleton(RolePolicyContract::class, RolePolicy::class);
        $this->app->singleton(SalesChannelPolicyContract::class, SalesChannelPolicy::class);
        $this->app->singleton(ShipmentPolicyContract::class, ShipmentPolicy::class);
        $this->app->singleton(ShipmentItemPolicyContract::class, ShipmentItemPolicy::class);
        $this->app->singleton(StatusPolicyContract::class, StatusPolicy::class);
        $this->app->singleton(VatRatePolicyContract::class, VatRatePolicy::class);
        $this->app->singleton(VendorOrderPolicyContract::class, VendorOrderPolicy::class);
        $this->app->singleton(VendorOrderItemPolicyContract::class, VendorOrderItemPolicy::class);
        $this->app->singleton(ProfitTaxRatePolicyContract::class, ProfitTaxRatePolicy::class);
        $this->app->singleton(BinPolicyContract::class, BinPolicy::class);
        $this->app->singleton(FinancePolicyContract::class, FinancePolicy::class);
        $this->app->singleton(ProductPolicyContract::class, ProductPolicy::class);
        $this->app->singleton(FinancePolicyContract::class, FinancePolicy::class);
        $this->app->singleton(BinPolicyContract::class, BinPolicy::class);
        $this->app->singleton(UserViewSettingsPolicyContract::class, UserViewSettingsPolicy::class);
        $this->app->singleton(CabinetCurrenciesPolicyContract::class, CabinetCurrenciesPolicy::class);
        $this->app->singleton(PackingPolicyContract::class, PackingPolicy::class);
        $this->app->singleton(ProductPackingPolicyContract::class, ProductPackingPolicy::class);
        $this->app->singleton(AttributeValuePolicyContract::class, AttributeValuePolicy::class);
        $this->app->singleton(ProductAttributePolicyContract::class, ProductAttributePolicy::class);
        $this->app->singleton(BarcodePolicyContract::class, BarcodePolicy::class);
        $this->app->singleton(WarehouseCellPolicyContract::class, WarehouseCellPolicy::class);
        $this->app->singleton(WarehouseAddressPolicyContract::class, WarehouseAddressPolicy::class);
        $this->app->singleton(WarehouseCellSizePolicyContract::class, WarehouseCellSizePolicy::class);
        $this->app->singleton(WarehouseStorageAreaPolicyContract::class, WarehouseStorageAreaPolicy::class);
        $this->app->singleton(WarehouseCellGroupPolicyContract::class, WarehouseCellGroupPolicy::class);
        $this->app->singleton(WarehouseGroupsPolicyContract::class, WarehouseGroupsPolicy::class);
        $this->app->singleton(WarehousePolicyContract::class, WarehousePolicy::class);
        $this->app->singleton(WarehousePhonePolicyContract::class, WarehousePhonePolicy::class);
        $this->app->singleton(WarehouseWorkSchedulePolicyContract::class, WarehouseWorkSchedulePolicy::class);
        $this->app->singleton(OzonV3PostingFbsListPolicyContract::class, OzonV3PostingFbsListPolicy::class);
        $this->app->singleton(ExcelImportPolicyContract::class, ExcelImportPolicy::class);
        $this->app->singleton(OzonCredentialsPolicyContract::class, OzonCredentialsPolicy::class);
        $this->app->singleton(OzonV3FinanceTransactionListPolicyContract::class, OzonV3FinanceTransactionListPolicy::class);
        $this->app->singleton(OzonV1ReturnsFboFbsListPolicyContract::class, OzonV1ReturnsFboFbsListPolicy::class);
        $this->app->singleton(OzonWarehousesPolicyContract::class, OzonWarehousesPolicy::class);
        $this->app->singleton(OzonV2ReturnsRfbsListPolicyContract::class, OzonV2ReturnsRfbsListPolicy::class);
        $this->app->singleton(ContractorGroupsPolicyContract::class, ContractorGroupsPolicy::class);
        $this->app->singleton(ProductGroupsPolicyContract::class, ProductGroupsPolicy::class);
        $this->app->singleton(DepartmentPermissionPolicyContract::class, DepartmentPermissionPolicy::class);
        $this->app->singleton(MeasurementUnitGroupPolicyContract::class, MeasurementUnitGroupPolicy::class);
        $this->app->singleton(WarehouseCalendarPolicyContract::class, WarehouseCalendarPolicy::class);
        $this->app->singleton(BookmarksPolicyContract::class, BookmarkPolicy::class);
        $this->app->singleton(ContractPolicyContract::class, ContractPolicy::class);
        $this->app->singleton(ComissionReportsPolicyContract::class, ComissionReportsPolicy::class);
        $this->app->singleton(IssuedComissionReportsPolicyContract::class, IssuedComissionReportsPolicy::class);
        $this->app->singleton(ReceivedComissionReportsPolicyContract::class, ReceivedComissionReportsPolicy::class);
        $this->app->singleton(ReceivedComissionReportsRealizedItemPolicyContract::class, ReceivedComissionReportRealizedItemPolicy::class);
        $this->app->singleton(ReceivedComissionReportsReturnItemPolicyContract::class, ReceivedComissionReportReturnItemPolicy::class);
        $this->app->singleton(IssuedComissionReportsItemPolicyContract::class, IssuedComissionReportItemPolicy::class);
        $this->app->singleton(GoodsTransferPolicyContract::class, GoodsTransferPolicy::class);
        $this->app->singleton(GoodsTransferItemPolicyContract::class, GoodsTransferItemPolicy::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
