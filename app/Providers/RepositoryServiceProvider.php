<?php

namespace App\Providers;

use App\Contracts\Repositories\AcceptanceItemsRepositoryContract;
use App\Contracts\Repositories\AcceptanceRepositoryContract;
use App\Contracts\Repositories\AttributeGroupsRepositoryContract;
use App\Contracts\Repositories\AttributesRepositoryContract;
use App\Contracts\Repositories\AttributeValuesRepositoryContract;
use App\Contracts\Repositories\BarcodesRepositoryContract;
use App\Contracts\Repositories\BookmarkRepositoryContract;
use App\Contracts\Repositories\BrandsRepositoryContract;
use App\Contracts\Repositories\CabinetCurrenciesRepositoryContract;
use App\Contracts\Repositories\CabinetEmployeeRepositoryContract;
use App\Contracts\Repositories\CabinetInvitesRepositoryContract;
use App\Contracts\Repositories\CabinetPricesRepositoryContract;
use App\Contracts\Repositories\CabinetSettingRepositoryContract;
use App\Contracts\Repositories\CabinetsRepositoryContract;
use App\Contracts\Repositories\ComissionReportsRepositoryContract;
use App\Contracts\Repositories\ContractorAccountsRepositoryContract;
use App\Contracts\Repositories\ContractorAdressesRepositoryContract;
use App\Contracts\Repositories\ContractorContactsRepositoryContract;
use App\Contracts\Repositories\ContractorContractorGroupRepositoryContract;
use App\Contracts\Repositories\ContractorDetailAddressRepositoryContract;
use App\Contracts\Repositories\ContractorDetailsRepositoryContract;
use App\Contracts\Repositories\ContractorGroupsRepositoryContract;
use App\Contracts\Repositories\ContractorsRepositoryContract;
use App\Contracts\Repositories\ContractRepositoryContract;
use App\Contracts\Repositories\CountriesRepositoryContract;
use App\Contracts\Repositories\CustomerOrderDeliveryRepositoryContract;
use App\Contracts\Repositories\CustomerOrderItemsRepositoryContract;
use App\Contracts\Repositories\CustomerOrdersRepositoryContract;
use App\Contracts\Repositories\DepartmentPermissionsRepositoryContract;
use App\Contracts\Repositories\DepartmentsRepositoryContract;
use App\Contracts\Repositories\DiscountContractorGroupRepositoryContract;
use App\Contracts\Repositories\DiscountProductsRepositoryContract;
use App\Contracts\Repositories\DiscountSavingsRepositoryContract;
use App\Contracts\Repositories\DiscountsRepositoryContract;
use App\Contracts\Repositories\DocumentsRepositoryContract;
use App\Contracts\Repositories\EmployeePermissionsRepositoryContract;
use App\Contracts\Repositories\EmployeeRepositoryContract;
use App\Contracts\Repositories\FileRelationsRepositoryContract;
use App\Contracts\Repositories\FilesRepositoryContract;
use App\Contracts\Repositories\FinanceRepositoryContract;
use App\Contracts\Repositories\GlobalCurrenciesRepositoryContract;
use App\Contracts\Repositories\GoodsTransferItemsRepositoryContract;
use App\Contracts\Repositories\GoodsTransferRepositoryContract;
use App\Contracts\Repositories\GoodsTransferWarehouseItemRepositoryContract;
use App\Contracts\Repositories\IncomingPaymentItemsRepositoryContract;
use App\Contracts\Repositories\IncomingPaymentsRepositoryContract;
use App\Contracts\Repositories\IssuedComissionReportsItemsRepositoryContract;
use App\Contracts\Repositories\IssuedComissionReportsRepositoryContract;
use App\Contracts\Repositories\LegalEntitiesRepositoryContract;
use App\Contracts\Repositories\MeasurementUnitGroupsRepositoryContract;
use App\Contracts\Repositories\MeasurementUnitsRepositoryContract;
use App\Contracts\Repositories\OutgoingPaymentItemsRepositoryContract;
use App\Contracts\Repositories\OutgoingPaymentsRepositoryContract;
use App\Contracts\Repositories\OzonCredentialsRepositoryContract;
use App\Contracts\Repositories\OzonOrderItemsRepositoryContract;
use App\Contracts\Repositories\OzonOrdersRepositoryContract;
use App\Contracts\Repositories\OzonProductsRepositoryContract;
use App\Contracts\Repositories\OzonV1ReturnsFboFbsListRepositoryContract;
use App\Contracts\Repositories\OzonV2ReturnsRfbsListRepositoryContract;
use App\Contracts\Repositories\OzonV3FinanceTransactionItemsRepositoryContract;
use App\Contracts\Repositories\OzonV3FinanceTransactionListRepositoryContract;
use App\Contracts\Repositories\OzonV3FinanceTransactionServicesRepositoryContract;
use App\Contracts\Repositories\OzonWarehousesRepositoryContract;
use App\Contracts\Repositories\PackingsRepositoryContract;
use App\Contracts\Repositories\PermissionsRepositoryContract;
use App\Contracts\Repositories\ProductAccountingFeaturesRepositoryContract;
use App\Contracts\Repositories\ProductAttributeRepositoryContract;
use App\Contracts\Repositories\ProductCategoriesRepositoryContract;
use App\Contracts\Repositories\ProductEgaisCodeRepositoryContract;
use App\Contracts\Repositories\ProductGroupsRepositoryContract;
use App\Contracts\Repositories\ProductPackingRepositoryContract;
use App\Contracts\Repositories\ProductPricesRepositoryContract;
use App\Contracts\Repositories\ProductsRepositoryContract;
use App\Contracts\Repositories\ProductThresholdsRepositoryContract;
use App\Contracts\Repositories\ProfitTaxRatesRepositoryContract;
use App\Contracts\Repositories\ReceivedComissionReportsRealizedItemsRepositoryContract;
use App\Contracts\Repositories\ReceivedComissionReportsRepositoryContract;
use App\Contracts\Repositories\ReceivedComissionReportsReturnItemsRepositoryContract;
use App\Contracts\Repositories\RelatedDocumentsRepositoryContract;
use App\Contracts\Repositories\RolePermissionsRepositoryContract;
use App\Contracts\Repositories\RolesRepositoryContract;
use App\Contracts\Repositories\SaleChannelTypesRepositoryContract;
use App\Contracts\Repositories\SalesChannelsRepositoryContract;
use App\Contracts\Repositories\ShipmentDeliveryRepositoryContract;
use App\Contracts\Repositories\ShipmentItemsRepositoryContract;
use App\Contracts\Repositories\ShipmentsRepositoryContract;
use App\Contracts\Repositories\ShipmentWarehouseItemRepositoryContract;
use App\Contracts\Repositories\StatusesRepositoryContract;
use App\Contracts\Repositories\UsersRepositoryContract;
use App\Contracts\Repositories\UserViewSettingsRepositoryContract;
use App\Contracts\Repositories\VatRatesRepositoryContract;
use App\Contracts\Repositories\VendorOrderItemsRepositoryContract;
use App\Contracts\Repositories\VendorOrdersRepositoryContract;
use App\Contracts\Repositories\WarehouseAddressesRepositoryContract;
use App\Contracts\Repositories\WarehouseCellGroupsRepositoryContract;
use App\Contracts\Repositories\WarehouseCellSizesRepositoryContract;
use App\Contracts\Repositories\WarehouseCellsRepositoryContract;
use App\Contracts\Repositories\WarehouseIssueOrderItemsRepositoryContract;
use App\Contracts\Repositories\WarehouseIssueOrdersRepositoryContract;
use App\Contracts\Repositories\WarehouseItemsRepositoryContract;
use App\Contracts\Repositories\WarehouseOrderSchemesRepositoryContract;
use App\Contracts\Repositories\WarehousePhonesRepositoryContract;
use App\Contracts\Repositories\WarehouseReceiptOrderItemsRepositoryContract;
use App\Contracts\Repositories\WarehouseReceiptOrdersRepositoryContract;
use App\Contracts\Repositories\WarehousesRepositoryContract;
use App\Contracts\Repositories\WarehouseStorageAreaCellsRepositoryContract;
use App\Contracts\Repositories\WarehouseStorageAreasRepositoryContract;
use App\Contracts\Repositories\WarehouseStructuresRepositoryContract;
use App\Contracts\Repositories\WorkSchedulesRepositoryContract;
use App\Repositories\Contractors\Contractors\ContractorAccountsRepository;
use App\Repositories\Contractors\Contractors\ContractorAdressesRepository;
use App\Repositories\Contractors\Contractors\ContractorContactsRepository;
use App\Repositories\Contractors\Contractors\ContractorsRepository;
use App\Repositories\Contractors\ContractRepository;
use App\Repositories\Contractors\Detail\ContractorDetailAddressRepository;
use App\Repositories\Contractors\Detail\ContractorDetailsRepository;
use App\Repositories\Contractors\Groups\ContractorContractorGroupRepository;
use App\Repositories\Contractors\Groups\ContractorGroupsRepository;
use App\Repositories\Documents\DocumentsRepository;
use App\Repositories\Documents\RelatedDocumentsRepository;
use App\Repositories\Files\FileRelationsRepository;
use App\Repositories\Files\FilesRepository;
use App\Repositories\Goods\Attributes\AttributeGroupsRepository;
use App\Repositories\Goods\Attributes\AttributesRepository;
use App\Repositories\Goods\Attributes\AttributeValuesRepository;
use App\Repositories\Goods\Other\BarcodesRepository;
use App\Repositories\Goods\Other\BrandsRepository;
use App\Repositories\Goods\Prices\CabinetPricesRepository;
use App\Repositories\Goods\Products\ProductAccountingFeaturesRepository;
use App\Repositories\Goods\Products\ProductAttributeRepository;
use App\Repositories\Goods\Products\ProductCategoriesRepository;
use App\Repositories\Goods\Products\ProductEgaisCodeRepository;
use App\Repositories\Goods\Products\ProductGroupsRepository;
use App\Repositories\Goods\Products\ProductPackingRepository;
use App\Repositories\Goods\Products\ProductPricesRepository;
use App\Repositories\Goods\Products\ProductsRepository;
use App\Repositories\Goods\Products\ProductThresholdsRepository;
use App\Repositories\Goods\Transfers\GoodsTransferItemsRepository;
use App\Repositories\Goods\Transfers\GoodsTransferRepository;
use App\Repositories\Goods\Transfers\GoodsTransferWarehouseItemRepository;
use App\Repositories\Money\FinancesRepository;
use App\Repositories\Money\IncomingPayments\IncomingPaymentItemsRepository;
use App\Repositories\Money\IncomingPayments\IncomingPaymentsRepository;
use App\Repositories\Money\OutgoingPayments\OutgoingPaymentItemsRepository;
use App\Repositories\Money\OutgoingPayments\OutgoingPaymentsRepository;
use App\Repositories\Other\Statuses\StatusesRepository;
use App\Repositories\Ozon\OzonCredentialsRepository;
use App\Repositories\Ozon\OzonOrderItemsRepository;
use App\Repositories\Ozon\OzonOrdersRepository;
use App\Repositories\Ozon\OzonProductsRepository;
use App\Repositories\Ozon\OzonV1ReturnsFboFbsListRepository;
use App\Repositories\Ozon\OzonV2ReturnsRfbsListRepository;
use App\Repositories\Ozon\OzonV3FinanceTransactionItemsRepository;
use App\Repositories\Ozon\OzonV3FinanceTransactionListRepository;
use App\Repositories\Ozon\OzonV3FinanceTransactionServicesRepository;
use App\Repositories\Ozon\OzonWarehousesRepository;
use App\Repositories\Procurement\Acceptances\AcceptanceItemsRepository;
use App\Repositories\Procurement\Acceptances\AcceptancesRepository;
use App\Repositories\Procurement\VendorOrders\VendorOrderItemsRepository;
use App\Repositories\Procurement\VendorOrders\VendorOrdersRepository;
use App\Repositories\References\CabinetCurrenciesRepository;
use App\Repositories\References\CountriesRepository;
use App\Repositories\References\Discounts\DiscountContractorGroupRepository;
use App\Repositories\References\Discounts\DiscountProductsRepository;
use App\Repositories\References\Discounts\DiscountSavingsRepository;
use App\Repositories\References\Discounts\DiscountsRepository;
use App\Repositories\References\GlobalCurrenciesRepository;
use App\Repositories\References\LegalEntitiesRepository;
use App\Repositories\References\MeasurementUnits\MeasurementUnitGroupsRepository;
use App\Repositories\References\MeasurementUnits\MeasurementUnitsRepository;
use App\Repositories\References\PackingsRepository;
use App\Repositories\References\ProfitTaxRatesRepository;
use App\Repositories\References\SalesChannels\SaleChannelTypesRepository;
use App\Repositories\References\SalesChannels\SalesChannelsRepository;
use App\Repositories\References\VatRatesRepository;
use App\Repositories\Sales\ComissionReports\ComissionReportsRepository;
use App\Repositories\Sales\ComissionReports\IssuedComissionReportsItemsRepository;
use App\Repositories\Sales\ComissionReports\IssuedComissionReportsRepository;
use App\Repositories\Sales\ComissionReports\ReceivedComissionReportsRealizedItemsRepository;
use App\Repositories\Sales\ComissionReports\ReceivedComissionReportsRepository;
use App\Repositories\Sales\ComissionReports\ReceivedComissionReportsReturnItemsRepository;
use App\Repositories\Sales\CustomerOrders\CustomerOrderDeliverysRepository;
use App\Repositories\Sales\CustomerOrders\CustomerOrderItemsRepository;
use App\Repositories\Sales\CustomerOrders\CustomerOrdersRepository;
use App\Repositories\Sales\Shipments\ShipmentDeliveryRepository;
use App\Repositories\Sales\Shipments\ShipmentItemsRepository;
use App\Repositories\Sales\Shipments\ShipmentsRepository;
use App\Repositories\Sales\Shipments\ShipmentWarehouseItemRepository;
use App\Repositories\User\UsersRepository;
use App\Repositories\User\UserViewSettingsRepository;
use App\Repositories\Warehouses\Cells\WarehouseCellSizesRepository;
use App\Repositories\Warehouses\Cells\WarehouseCellsRepository;
use App\Repositories\Warehouses\StorageAreas\WarehouseStorageAreaCellsRepository;
use App\Repositories\Warehouses\StorageAreas\WarehouseStorageAreasRepository;
use App\Repositories\Warehouses\WarehouseAddressesRepository;
use App\Repositories\Warehouses\WarehouseCellGroupsRepository;
use App\Repositories\Warehouses\WarehouseIssueOrderItemsRepository;
use App\Repositories\Warehouses\WarehouseIssueOrdersRepository;
use App\Repositories\Warehouses\WarehouseItemsRepository;
use App\Repositories\Warehouses\WarehouseOrderSchemesRepository;
use App\Repositories\Warehouses\WarehousePhonesRepository;
use App\Repositories\Warehouses\WarehouseReceiptOrderItemsRepository;
use App\Repositories\Warehouses\WarehouseReceiptOrdersRepository;
use App\Repositories\Warehouses\WarehousesRepository;
use App\Repositories\Warehouses\WarehouseStructuresRepository;
use App\Repositories\Warehouses\WarehouseWorkSchedulesRepository;
use App\Repositories\Workspace\BookmarkRepository;
use App\Repositories\Workspace\Cabinet\CabinetInvitesRepository;
use App\Repositories\Workspace\Cabinet\CabinetSettingsRepository;
use App\Repositories\Workspace\Cabinet\CabinetsRepository;
use App\Repositories\Workspace\CabinetEmployeeRepository;
use App\Repositories\Workspace\DepartmentsRepository;
use App\Repositories\Workspace\EmployeesRepository;
use App\Repositories\Workspace\Permissions\DepartmentPermissionsRepository;
use App\Repositories\Workspace\Permissions\EmployeePermissionsRepository;
use App\Repositories\Workspace\Permissions\PermissionsRepository;
use App\Repositories\Workspace\Permissions\Roles\RolePermissionsRepository;
use App\Repositories\Workspace\Permissions\Roles\RolesRepository;
use Illuminate\Support\ServiceProvider;

class RepositoryServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(ProductsRepositoryContract::class, ProductsRepository::class);

        //Приемки
        $this->app->singleton(AcceptanceRepositoryContract::class, AcceptancesRepository::class);
        $this->app->singleton(AcceptanceItemsRepositoryContract::class, AcceptanceItemsRepository::class);

        //Складские позиции
        $this->app->singleton(WarehouseItemsRepositoryContract::class, WarehouseItemsRepository::class);

        //Заказы покупателей
        $this->app->singleton(CustomerOrdersRepositoryContract::class, CustomerOrdersRepository::class);
        $this->app->singleton(CustomerOrderItemsRepositoryContract::class, CustomerOrderItemsRepository::class);
        $this->app->singleton(CustomerOrderDeliveryRepositoryContract::class, CustomerOrderDeliverysRepository::class);

        //Отгрузки
        $this->app->singleton(ShipmentsRepositoryContract::class, ShipmentsRepository::class);
        $this->app->singleton(ShipmentItemsRepositoryContract::class, ShipmentItemsRepository::class);
        $this->app->singleton(ShipmentDeliveryRepositoryContract::class, ShipmentDeliveryRepository::class);

        //Заказы поставщикам
        $this->app->singleton(VendorOrdersRepositoryContract::class, VendorOrdersRepository::class);
        $this->app->singleton(VendorOrderItemsRepositoryContract::class, VendorOrderItemsRepository::class);

        //Shipment - Warehouse_item
        $this->app->singleton(ShipmentWarehouseItemRepositoryContract::class, ShipmentWarehouseItemRepository::class);

        //Measurement Units
        $this->app->singleton(MeasurementUnitsRepositoryContract::class, MeasurementUnitsRepository::class);
        $this->app->singleton(MeasurementUnitGroupsRepositoryContract::class, MeasurementUnitGroupsRepository::class);

        //Cabinet invites
        $this->app->singleton(CabinetInvitesRepositoryContract::class, CabinetInvitesRepository::class);

        //Cabinet employee
        $this->app->singleton(CabinetEmployeeRepositoryContract::class, CabinetEmployeeRepository::class);
        $this->app->singleton(EmployeeRepositoryContract::class, EmployeesRepository::class);

        //Users
        $this->app->singleton(UsersRepositoryContract::class, UsersRepository::class);

        //Currencies
        $this->app->singleton(GlobalCurrenciesRepositoryContract::class, GlobalCurrenciesRepository::class);
        $this->app->singleton(CabinetCurrenciesRepositoryContract::class, CabinetCurrenciesRepository::class);

        //Countries
        $this->app->singleton(CountriesRepositoryContract::class, CountriesRepository::class);

        //Sales channels
        $this->app->singleton(SalesChannelsRepositoryContract::class, SalesChannelsRepository::class);
        $this->app->singleton(SaleChannelTypesRepositoryContract::class, SaleChannelTypesRepository::class);

        //VAT rates
        $this->app->singleton(VatRatesRepositoryContract::class, VatRatesRepository::class);

        //Warehouse work schedules
        $this->app->singleton(WorkSchedulesRepositoryContract::class, WarehouseWorkSchedulesRepository::class);

        //Warehouse
        $this->app->singleton(WarehousesRepositoryContract::class, WarehousesRepository::class);
        $this->app->singleton(WarehousePhonesRepositoryContract::class, WarehousePhonesRepository::class);
        $this->app->singleton(WarehouseAddressesRepositoryContract::class, WarehouseAddressesRepository::class);
        $this->app->singleton(WarehouseStructuresRepositoryContract::class, WarehouseStructuresRepository::class);
        $this->app->singleton(WarehouseOrderSchemesRepositoryContract::class, WarehouseOrderSchemesRepository::class);
        $this->app->singleton(WarehouseReceiptOrdersRepositoryContract::class, WarehouseReceiptOrdersRepository::class);
        $this->app->singleton(WarehouseReceiptOrderItemsRepositoryContract::class, WarehouseReceiptOrderItemsRepository::class);
        $this->app->singleton(WarehouseIssueOrdersRepositoryContract::class, WarehouseIssueOrdersRepository::class);
        $this->app->singleton(WarehouseIssueOrderItemsRepositoryContract::class, WarehouseIssueOrderItemsRepository::class);
        $this->app->singleton(WarehouseTransactionsRepositoryContract::class, WarehouseTransactionsRepository::class);

        $this->app->singleton(WarehouseCellsRepositoryContract::class, WarehouseCellsRepository::class);
        $this->app->singleton(WarehouseStorageAreaCellsRepositoryContract::class, WarehouseStorageAreaCellsRepository::class);
        $this->app->singleton(WarehouseCellSizesRepositoryContract::class, WarehouseCellSizesRepository::class);
        $this->app->singleton(WarehouseCellGroupsRepositoryContract::class, WarehouseCellGroupsRepository::class);
        $this->app->singleton(WarehouseStorageAreasRepositoryContract::class, WarehouseStorageAreasRepository::class);

        //Платежи
        $this->app->singleton(DocumentsRepositoryContract::class, DocumentsRepository::class);

        $this->app->singleton(IncomingPaymentsRepositoryContract::class, IncomingPaymentsRepository::class);
        $this->app->singleton(IncomingPaymentItemsRepositoryContract::class, IncomingPaymentItemsRepository::class);

        $this->app->singleton(OutgoingPaymentsRepositoryContract::class, OutgoingPaymentsRepository::class);
        $this->app->singleton(OutgoingPaymentItemsRepositoryContract::class, OutgoingPaymentItemsRepository::class);
        $this->app->singleton(RolesRepositoryContract::class, RolesRepository::class);
        $this->app->singleton(PermissionsRepositoryContract::class, PermissionsRepository::class);
        $this->app->singleton(RolePermissionsRepositoryContract::class, RolePermissionsRepository::class);
        $this->app->singleton(EmployeePermissionsRepositoryContract::class, EmployeePermissionsRepository::class);
        $this->app->singleton(DepartmentPermissionsRepositoryContract::class, DepartmentPermissionsRepository::class);

        $this->app->singleton(StatusesRepositoryContract::class, StatusesRepository::class);
        $this->app->singleton(LegalEntitiesRepositoryContract::class, LegalEntitiesRepository::class);
        $this->app->singleton(ContractorsRepositoryContract::class, ContractorsRepository::class);
        $this->app->singleton(ProductCategoriesRepositoryContract::class, ProductCategoriesRepository::class);
        $this->app->singleton(RelatedDocumentsRepositoryContract::class, RelatedDocumentsRepository::class);
        $this->app->singleton(AttributesRepositoryContract::class, AttributesRepository::class);
        $this->app->singleton(AttributeGroupsRepositoryContract::class, AttributeGroupsRepository::class);
        $this->app->singleton(FilesRepositoryContract::class, FilesRepository::class);
        $this->app->singleton(CabinetsRepositoryContract::class, CabinetsRepository::class);
        $this->app->singleton(BrandsRepositoryContract::class, BrandsRepository::class);
        $this->app->singleton(CabinetPricesRepositoryContract::class, CabinetPricesRepository::class);
        $this->app->singleton(CabinetSettingRepositoryContract::class, CabinetSettingsRepository::class);
        $this->app->singleton(ContractorsRepositoryContract::class, ContractorsRepository::class);
        $this->app->singleton(ContractorAdressesRepositoryContract::class, ContractorAdressesRepository::class);
        $this->app->singleton(ContractorDetailsRepositoryContract::class, ContractorDetailsRepository::class);
        $this->app->singleton(ContractorDetailAddressRepositoryContract::class, ContractorDetailAddressRepository::class);
        $this->app->singleton(DepartmentsRepositoryContract::class, DepartmentsRepository::class);
        $this->app->singleton(DiscountsRepositoryContract::class, DiscountsRepository::class);
        $this->app->singleton(PackingsRepositoryContract::class, PackingsRepository::class);
        $this->app->singleton(ProfitTaxRatesRepositoryContract::class, ProfitTaxRatesRepository::class);
        $this->app->singleton(BarcodesRepositoryContract::class, BarcodesRepository::class);
        $this->app->singleton(ProductPricesRepositoryContract::class, ProductPricesRepository::class);
        $this->app->singleton(ProductAccountingFeaturesRepositoryContract::class, ProductAccountingFeaturesRepository::class);
        $this->app->singleton(ProductThresholdsRepositoryContract::class, ProductThresholdsRepository::class);
        $this->app->singleton(ProductEgaisCodeRepositoryContract::class, ProductEgaisCodeRepository::class);
        $this->app->singleton(ProductAttributeRepositoryContract::class, ProductAttributeRepository::class);
        $this->app->singleton(AttributeValuesRepositoryContract::class, AttributeValuesRepository::class);
        $this->app->singleton(UserViewSettingsRepositoryContract::class, UserViewSettingsRepository::class);
        $this->app->singleton(CabinetCurrenciesRepositoryContract::class, CabinetCurrenciesRepository::class);
        $this->app->singleton(OzonProductsRepositoryContract::class, OzonProductsRepository::class);
        $this->app->singleton(ProductPackingRepositoryContract::class, ProductPackingRepository::class);
        $this->app->singleton(OzonOrdersRepositoryContract::class, OzonOrdersRepository::class);
        $this->app->singleton(OzonOrderItemsRepositoryContract::class, OzonOrderItemsRepository::class);
        $this->app->singleton(FinanceRepositoryContract::class, FinancesRepository::class);
        $this->app->singleton(FileRelationsRepositoryContract::class, FileRelationsRepository::class);
        $this->app->singleton(OzonCredentialsRepositoryContract::class, OzonCredentialsRepository::class);
        $this->app->singleton(OzonV3FinanceTransactionListRepositoryContract::class, OzonV3FinanceTransactionListRepository::class);
        $this->app->singleton(OzonV3FinanceTransactionItemsRepositoryContract::class, OzonV3FinanceTransactionItemsRepository::class);
        $this->app->singleton(OzonV3FinanceTransactionServicesRepositoryContract::class, OzonV3FinanceTransactionServicesRepository::class);
        $this->app->singleton(OzonV1ReturnsFboFbsListRepositoryContract::class, OzonV1ReturnsFboFbsListRepository::class);
        $this->app->singleton(OzonWarehousesRepositoryContract::class, OzonWarehousesRepository::class);
        $this->app->singleton(OzonV2ReturnsRfbsListRepositoryContract::class, OzonV2ReturnsRfbsListRepository::class);
        $this->app->singleton(ContractorGroupsRepositoryContract::class, ContractorGroupsRepository::class);
        $this->app->singleton(ProductGroupsRepositoryContract::class, ProductGroupsRepository::class);
        $this->app->singleton(ContractorContractorGroupRepositoryContract::class, ContractorContractorGroupRepository::class);
        $this->app->singleton(ContractorAccountsRepositoryContract::class, ContractorAccountsRepository::class);
        $this->app->singleton(ContractorContactsRepositoryContract::class, ContractorContactsRepository::class);
        $this->app->singleton(DiscountContractorGroupRepositoryContract::class, DiscountContractorGroupRepository::class);
        $this->app->singleton(DiscountSavingsRepositoryContract::class, DiscountSavingsRepository::class);
        $this->app->singleton(DiscountProductsRepositoryContract::class, DiscountProductsRepository::class);
        $this->app->singleton(BookmarkRepositoryContract::class, BookmarkRepository::class);
        $this->app->singleton(GoodsTransferRepositoryContract::class, GoodsTransferRepository::class);
        $this->app->singleton(GoodsTransferWarehouseItemRepositoryContract::class, GoodsTransferWarehouseItemRepository::class);
        $this->app->singleton(GoodsTransferItemsRepositoryContract::class, GoodsTransferItemsRepository::class);
        $this->app->singleton(ContractRepositoryContract::class, ContractRepository::class);
        $this->app->singleton(ComissionReportsRepositoryContract::class, ComissionReportsRepository::class);
        $this->app->singleton(ReceivedComissionReportsRepositoryContract::class, ReceivedComissionReportsRepository::class);
        $this->app->singleton(IssuedComissionReportsRepositoryContract::class, IssuedComissionReportsRepository::class);
        $this->app->singleton(ReceivedComissionReportsRealizedItemsRepositoryContract::class, ReceivedComissionReportsRealizedItemsRepository::class);
        $this->app->singleton(ReceivedComissionReportsReturnItemsRepositoryContract::class, ReceivedComissionReportsReturnItemsRepository::class);
        $this->app->singleton(IssuedComissionReportsItemsRepositoryContract::class, IssuedComissionReportsItemsRepository::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
