<?php

namespace App\Entities;

class WarehouseIssueOrderEntity extends BaseEntity
{
    public static string $table = 'warehouse_issue_orders';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'cabinet_id',
        'employee_id',
        'department_id',
        'warehouse_id',
        'number',
        'date_from',
        'status_id',
        'held',
        'document_basis_type',
        'document_basis_id',
        'reason',
        'write_off_reason',
        'total_quantity',
        'comment',
    ];

    public function cabinet(): RelationBuilder
    {
        return $this->hasOne(CabinetEntity::class, 'cabinet_id', 'id');
    }

    public function employee(): RelationBuilder
    {
        return $this->hasOne(EmployeeEntity::class, 'employee_id', 'id');
    }

    public function department(): RelationBuilder
    {
        return $this->hasOne(DepartmentEntity::class, 'department_id', 'id');
    }

    public function warehouse(): RelationBuilder
    {
        return $this->hasOne(WarehouseEntity::class, 'warehouse_id', 'id');
    }

    public function status(): RelationBuilder
    {
        return $this->hasOne(StatusEntity::class, 'status_id', 'id');
    }

    public function items(): RelationBuilder
    {
        return $this->hasMany(WarehouseIssueOrderItemEntity::class, 'id', 'issue_order_id');
    }
}
