<?php

namespace App\Entities;

class WarehouseOrderSchemeEntity extends BaseEntity
{
    public static string $table = 'warehouse_order_schemes';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'warehouse_id',
        'on_coming_from',
        'on_shipment_from',
        'control_operational_balances',
    ];

    public function warehouse(): RelationBuilder
    {
        return $this->hasOne(WarehouseEntity::class, 'warehouse_id', 'id');
    }
}
