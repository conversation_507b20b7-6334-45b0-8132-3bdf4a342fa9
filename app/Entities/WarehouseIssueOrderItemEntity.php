<?php

namespace App\Entities;

class WarehouseIssueOrderItemEntity extends BaseEntity
{
    public static string $table = 'warehouse_issue_order_items';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'issue_order_id',
        'product_id',
        'warehouse_item_id',
        'quantity',
        'unit_price',
        'total_cost',
        'expiry_date',
        'batch_number',
        'serial_number',
        'comment',
    ];

    public function issueOrder(): RelationBuilder
    {
        return $this->hasOne(WarehouseIssueOrderEntity::class, 'issue_order_id', 'id');
    }

    public function product(): RelationBuilder
    {
        return $this->hasOne(ProductEntity::class, 'product_id', 'id');
    }

    public function warehouseItem(): RelationBuilder
    {
        return $this->hasOne(WarehouseItemEntity::class, 'warehouse_item_id', 'id');
    }
}
