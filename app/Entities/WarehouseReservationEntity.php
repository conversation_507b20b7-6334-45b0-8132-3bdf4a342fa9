<?php

namespace App\Entities;

class WarehouseReservationEntity extends BaseEntity
{
    public static string $table = 'warehouse_reservations';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'warehouse_id',
        'product_id',
        'warehouse_item_id',
        'quantity',
        'reserved_quantity',
        'used_quantity',
        'document_type',
        'document_id',
        'reservation_type',
        'priority',
        'reserved_at',
        'expires_at',
        'status',
        'batch_number',
        'lot_number',
        'expiry_date',
        'created_by',
        'cancelled_by',
        'cancelled_at',
        'cancellation_reason',
    ];

    public function warehouse(): RelationBuilder
    {
        return $this->hasOne(WarehouseEntity::class, 'warehouse_id', 'id');
    }

    public function product(): RelationBuilder
    {
        return $this->hasOne(ProductEntity::class, 'product_id', 'id');
    }

    public function warehouseItem(): RelationBuilder
    {
        return $this->hasOne(WarehouseItemEntity::class, 'warehouse_item_id', 'id');
    }

    public function createdBy(): RelationBuilder
    {
        return $this->hasOne(EmployeeEntity::class, 'created_by', 'id');
    }

    public function cancelledBy(): RelationBuilder
    {
        return $this->hasOne(EmployeeEntity::class, 'cancelled_by', 'id');
    }

    public function transactions(): RelationBuilder
    {
        return $this->hasMany(WarehouseTransactionEntity::class, 'id', 'reservation_id');
    }
}
