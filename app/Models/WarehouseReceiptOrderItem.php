<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WarehouseReceiptOrderItem extends Model
{
    use HasUuids;

    protected $table = 'warehouse_receipt_order_items';

    protected $fillable = [
        'receipt_order_id',
        'product_id',
        'warehouse_item_id',
        'quantity',
        'unit_price',
        'total_cost',
        'expiry_date',
        'batch_number',
        'serial_number',
        'comment',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'expiry_date' => 'date',
    ];

    public function receiptOrder(): BelongsTo
    {
        return $this->belongsTo(WarehouseReceiptOrder::class, 'receipt_order_id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function warehouseItem(): BelongsTo
    {
        return $this->belongsTo(WarehouseItem::class);
    }
}
