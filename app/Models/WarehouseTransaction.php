<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WarehouseTransaction extends Model
{
    use HasFactory;
    use HasUuids;

    protected $fillable = [
        // Старые поля для обратной совместимости
        'item_id',
        'shipment_id',
        'transaction_type',
        'quantity',
        'total_cost',
        'transaction_date',

        // Новые поля для ордерной схемы
        'warehouse_id',
        'product_id',
        'warehouse_item_id',
        'document_type',
        'document_id',
        'operation_type',
        'batch_number',
        'lot_number',
        'expiry_date',
        'quality_status',
        'cost_per_unit',
        'reservation_id',
    ];

    protected $casts = [
        'transaction_date' => 'datetime',
        'expiry_date' => 'date',
        'quantity' => 'integer',
    ];

    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function warehouseItem(): BelongsTo
    {
        return $this->belongsTo(WarehouseItem::class);
    }

    public function reservation(): BelongsTo
    {
        return $this->belongsTo(WarehouseReservation::class, 'reservation_id');
    }

    public function shipment(): BelongsTo
    {
        return $this->belongsTo(Shipment::class);
    }

    // Старые связи для обратной совместимости
    public function item(): BelongsTo
    {
        return $this->belongsTo(WarehouseItem::class, 'item_id');
    }
}
