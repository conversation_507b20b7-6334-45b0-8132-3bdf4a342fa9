<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WarehouseIssueOrderItem extends Model
{
    use HasUuids;

    protected $table = 'warehouse_issue_order_items';

    protected $fillable = [
        'issue_order_id',
        'product_id',
        'warehouse_item_id',
        'quantity',
        'unit_price',
        'total_cost',
        'expiry_date',
        'batch_number',
        'serial_number',
        'comment',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'expiry_date' => 'date',
    ];

    public function issueOrder(): BelongsTo
    {
        return $this->belongsTo(WarehouseIssueOrder::class, 'issue_order_id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function warehouseItem(): BelongsTo
    {
        return $this->belongsTo(WarehouseItem::class);
    }
}
