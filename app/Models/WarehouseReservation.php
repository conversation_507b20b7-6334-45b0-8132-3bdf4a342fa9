<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class WarehouseReservation extends Model
{
    use HasUuids;

    protected $table = 'warehouse_reservations';

    protected $fillable = [
        'warehouse_id',
        'product_id',
        'warehouse_item_id',
        'quantity',
        'reserved_quantity',
        'used_quantity',
        'document_type',
        'document_id',
        'reservation_type',
        'priority',
        'reserved_at',
        'expires_at',
        'status',
        'batch_number',
        'lot_number',
        'expiry_date',
        'created_by',
        'cancelled_by',
        'cancelled_at',
        'cancellation_reason',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'reserved_quantity' => 'integer',
        'used_quantity' => 'integer',
        'priority' => 'integer',
        'reserved_at' => 'datetime',
        'expires_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'expiry_date' => 'date',
    ];

    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function warehouseItem(): BelongsTo
    {
        return $this->belongsTo(WarehouseItem::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'created_by');
    }

    public function cancelledBy(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'cancelled_by');
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(WarehouseTransaction::class, 'reservation_id');
    }

    // Вычисляемые поля
    public function getAvailableQuantityAttribute(): int
    {
        return $this->reserved_quantity - $this->used_quantity;
    }

    public function getRemainingQuantityAttribute(): int
    {
        return $this->quantity - $this->reserved_quantity;
    }

    // Методы состояния
    public function isActive(): bool
    {
        return $this->status === 'active' && !$this->isExpired();
    }

    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    public function isPartial(): bool
    {
        return $this->reserved_quantity > 0 && $this->reserved_quantity < $this->quantity;
    }

    public function isFulfilled(): bool
    {
        return $this->reserved_quantity >= $this->quantity;
    }

    public function canBeUsed(int $quantity): bool
    {
        return $this->isActive() && $this->getAvailableQuantityAttribute() >= $quantity;
    }

    // Методы управления резервом
    public function reserve(int $quantity): bool
    {
        if ($this->getRemainingQuantityAttribute() < $quantity) {
            return false;
        }

        $this->reserved_quantity += $quantity;
        $this->updateStatus();
        
        return $this->save();
    }

    public function use(int $quantity): bool
    {
        if (!$this->canBeUsed($quantity)) {
            return false;
        }

        $this->used_quantity += $quantity;
        $this->updateStatus();
        
        return $this->save();
    }

    public function cancel(string $reason = null, string $cancelledBy = null): bool
    {
        $this->status = 'cancelled';
        $this->cancelled_at = Carbon::now();
        $this->cancellation_reason = $reason;
        
        if ($cancelledBy) {
            $this->cancelled_by = $cancelledBy;
        }
        
        return $this->save();
    }

    private function updateStatus(): void
    {
        if ($this->isExpired()) {
            $this->status = 'expired';
        } elseif ($this->isFulfilled()) {
            $this->status = $this->used_quantity >= $this->reserved_quantity ? 'fulfilled' : 'active';
        } elseif ($this->isPartial()) {
            $this->status = 'partial';
        } else {
            $this->status = 'active';
        }
    }
}
