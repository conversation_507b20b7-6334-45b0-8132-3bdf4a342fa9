<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class WarehouseItem extends Model
{
    use HasFactory;
    use HasUuids;

    protected $fillable = [
        'warehouse_id',
        'cell_id',
        'product_id',
        'acceptance_id',
        'batch_number',
        'quantity',
        'unit_price',
        'total_price',
        'received_at',
        'status',

        // Новые поля для ордерной схемы
        'lot_number',
        'expiry_date',
        'supplier_batch',
        'quality_status',
        'reserved_quantity',
        'last_movement_date',
        'storage_location',
    ];

    protected $casts = [
        'received_at' => 'datetime',
        'expiry_date' => 'date',
        'last_movement_date' => 'datetime',
        'quantity' => 'integer',
        'reserved_quantity' => 'integer',
    ];

    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function cell(): BelongsTo
    {
        return $this->belongsTo(WarehouseCell::class);
    }

    public function acceptance(): BelongsTo
    {
        return $this->belongsTo(Acceptance::class);
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(WarehouseTransaction::class, 'warehouse_item_id');
    }

    public function reservations(): HasMany
    {
        return $this->hasMany(WarehouseReservation::class);
    }

    // Вычисляемые поля
    public function getAvailableQuantityAttribute(): int
    {
        return $this->quantity - $this->reserved_quantity;
    }

    public function isExpired(): bool
    {
        return $this->expiry_date && $this->expiry_date->isPast();
    }

    public function isExpiringSoon(int $days = 30): bool
    {
        return $this->expiry_date && $this->expiry_date->diffInDays(now()) <= $days;
    }
}
