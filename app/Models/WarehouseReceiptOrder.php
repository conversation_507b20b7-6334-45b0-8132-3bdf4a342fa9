<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class WarehouseReceiptOrder extends Model
{
    use HasUuids;

    protected $table = 'warehouse_receipt_orders';

    protected $fillable = [
        'cabinet_id',
        'employee_id',
        'department_id',
        'warehouse_id',
        'number',
        'date_from',
        'status_id',
        'held',
        'document_basis_type',
        'document_basis_id',
        'reason',
        'total_quantity',
        'comment',
    ];

    protected $casts = [
        'date_from' => 'datetime',
        'held' => 'boolean',
        'total_quantity' => 'integer',
    ];

    public function cabinet(): BelongsTo
    {
        return $this->belongsTo(Cabinet::class);
    }

    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function status(): BelongsTo
    {
        return $this->belongsTo(Status::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(WarehouseReceiptOrderItem::class, 'receipt_order_id');
    }
}
