<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('statuses', function ($table) {
            $table->uuid('id')->primary();
            $table->timestamps();
            $table->softDeletes();

            $table->string('name');
            $table->string('color');
            $table->foreignUuid('cabinet_id')->nullable()->constrained()->cascadeOnDelete();
            $table->integer('type');
            $table->integer('sort')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('role_permissions');
        Schema::dropIfExists('employee_permissions');
        Schema::dropIfExists('departament_permissions');
        Schema::table('statuses', function (Blueprint $table) {
            if (Schema::hasColumn('statuses', 'cabinet_id')) {
                $table->dropForeign(['cabinet_id']);
            }
        });
        Schema::dropIfExists('roles');
        Schema::dropIfExists('permissions');
        Schema::dropIfExists('permission_categories');
        Schema::dropIfExists('permission_groups');
    }
};
