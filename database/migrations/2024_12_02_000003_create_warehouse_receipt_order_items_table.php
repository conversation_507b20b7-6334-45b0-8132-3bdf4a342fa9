<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('warehouse_receipt_order_items', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();
            
            $table->uuid('receipt_order_id');
            $table->uuid('product_id');
            $table->uuid('warehouse_item_id')->nullable();
            
            $table->integer('quantity');
            $table->string('unit_price', 20);
            $table->string('total_cost', 20);
            
            $table->date('expiry_date')->nullable();
            $table->string('batch_number')->nullable();
            $table->string('serial_number')->nullable();
            
            $table->text('comment')->nullable();
            
            $table->foreign('receipt_order_id')->references('id')->on('warehouse_receipt_orders')->onDelete('cascade');
            $table->foreign('product_id')->references('id')->on('products');
            $table->foreign('warehouse_item_id')->references('id')->on('warehouse_items');
            
            $table->index(['receipt_order_id', 'product_id']);
            $table->index('warehouse_item_id');
            $table->index('expiry_date');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('warehouse_receipt_order_items');
    }
};
