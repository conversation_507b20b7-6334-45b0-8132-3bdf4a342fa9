<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('warehouse_issue_orders', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();
            
            $table->uuid('cabinet_id');
            $table->uuid('employee_id');
            $table->uuid('department_id');
            $table->uuid('warehouse_id');
            
            $table->string('number')->unique();
            $table->datetime('date_from');
            $table->uuid('status_id');
            $table->boolean('held')->default(false);
            
            $table->string('document_basis_type')->nullable();
            $table->uuid('document_basis_id')->nullable();
            $table->text('reason')->nullable();
            $table->string('write_off_reason')->nullable();
            
            $table->integer('total_quantity')->default(0);
            $table->text('comment')->nullable();
            
            $table->foreign('cabinet_id')->references('id')->on('cabinets');
            $table->foreign('employee_id')->references('id')->on('employees');
            $table->foreign('department_id')->references('id')->on('departments');
            $table->foreign('warehouse_id')->references('id')->on('warehouses');
            $table->foreign('status_id')->references('id')->on('statuses');
            
            $table->index(['warehouse_id', 'date_from']);
            $table->index(['cabinet_id', 'date_from']);
            $table->index(['document_basis_type', 'document_basis_id']);
            $table->index('held');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('warehouse_issue_orders');
    }
};
