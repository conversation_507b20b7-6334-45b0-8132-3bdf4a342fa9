<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('warehouse_transactions', function (Blueprint $table) {
            // Добавляем новые поля для ордерной схемы
            $table->uuid('warehouse_id')->nullable()->after('item_id');
            $table->uuid('product_id')->nullable()->after('warehouse_id');
            $table->uuid('warehouse_item_id')->nullable()->after('product_id');
            
            // Документ-основание
            $table->string('document_type')->nullable()->after('warehouse_item_id');
            $table->uuid('document_id')->nullable()->after('document_type');
            
            // Тип операции
            $table->enum('operation_type', [
                'receipt',
                'issue', 
                'transfer_out',
                'transfer_in',
                'reserve',
                'unreserve',
                'adjustment_plus',
                'adjustment_minus',
                'quality_hold',
                'quality_release'
            ])->nullable()->after('document_id');
            
            // Партионная информация
            $table->string('batch_number')->nullable()->after('operation_type');
            $table->string('lot_number')->nullable()->after('batch_number');
            $table->date('expiry_date')->nullable()->after('lot_number');
            
            // Статус качества
            $table->enum('quality_status', [
                'good',
                'defective', 
                'quarantine',
                'expired'
            ])->default('good')->after('expiry_date');
            
            // Финансовая информация
            $table->string('cost_per_unit', 20)->nullable()->after('quality_status');
            
            // Связь с резервом
            $table->uuid('reservation_id')->nullable()->after('cost_per_unit');
            
            // Индексы для оптимизации
            $table->index(['document_type', 'document_id']);
            $table->index('operation_type');
            $table->index('batch_number');
            $table->index('lot_number');
            $table->index('expiry_date');
            $table->index('quality_status');
            $table->index('reservation_id');
        });
    }

    public function down(): void
    {
        Schema::table('warehouse_transactions', function (Blueprint $table) {
            $table->dropIndex(['document_type', 'document_id']);
            $table->dropIndex(['operation_type']);
            $table->dropIndex(['batch_number']);
            $table->dropIndex(['lot_number']);
            $table->dropIndex(['expiry_date']);
            $table->dropIndex(['quality_status']);
            $table->dropIndex(['reservation_id']);
            
            $table->dropColumn([
                'warehouse_id',
                'product_id',
                'warehouse_item_id',
                'document_type',
                'document_id',
                'operation_type',
                'batch_number',
                'lot_number',
                'expiry_date',
                'quality_status',
                'cost_per_unit',
                'reservation_id'
            ]);
        });
    }
};
