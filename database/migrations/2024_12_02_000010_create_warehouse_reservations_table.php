<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('warehouse_reservations', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();
            
            // Основная информация
            $table->uuid('warehouse_id');
            $table->uuid('product_id');
            $table->uuid('warehouse_item_id')->nullable();
            
            // Количества
            $table->integer('quantity');
            $table->integer('reserved_quantity')->default(0);
            $table->integer('used_quantity')->default(0);
            
            // Документ-основание
            $table->string('document_type');
            $table->uuid('document_id');
            
            // Тип и приоритет резерва
            $table->enum('reservation_type', [
                'order',
                'production', 
                'transfer',
                'marketing',
                'quality'
            ])->default('order');
            
            $table->integer('priority')->default(5);
            
            // Временные рамки
            $table->datetime('reserved_at');
            $table->datetime('expires_at')->nullable();
            
            // Статус резерва
            $table->enum('status', [
                'active',
                'partial',
                'fulfilled',
                'expired',
                'cancelled'
            ])->default('active');
            
            // Партионная информация
            $table->string('batch_number')->nullable();
            $table->string('lot_number')->nullable();
            $table->date('expiry_date')->nullable();
            
            // Ответственные
            $table->uuid('created_by');
            $table->uuid('cancelled_by')->nullable();
            $table->datetime('cancelled_at')->nullable();
            $table->text('cancellation_reason')->nullable();
            
            // Внешние ключи
            $table->foreign('warehouse_id')->references('id')->on('warehouses');
            $table->foreign('product_id')->references('id')->on('products');
            $table->foreign('warehouse_item_id')->references('id')->on('warehouse_items');
            $table->foreign('created_by')->references('id')->on('employees');
            $table->foreign('cancelled_by')->references('id')->on('employees');
            
            // Индексы для оптимизации
            $table->index(['warehouse_id', 'product_id']);
            $table->index(['document_type', 'document_id']);
            $table->index(['status', 'expires_at']);
            $table->index(['reservation_type', 'priority']);
            $table->index(['batch_number', 'lot_number']);
            $table->index('reserved_at');
            $table->index('warehouse_item_id');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('warehouse_reservations');
    }
};
