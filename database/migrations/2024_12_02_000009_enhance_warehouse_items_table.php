<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('warehouse_items', function (Blueprint $table) {
            // Партионная информация
            $table->string('lot_number')->nullable()->after('quantity');
            $table->date('expiry_date')->nullable()->after('lot_number');
            $table->string('supplier_batch')->nullable()->after('expiry_date');
            
            // Статус качества
            $table->enum('quality_status', [
                'good',
                'defective',
                'quarantine', 
                'expired'
            ])->default('good')->after('supplier_batch');
            
            // Количества для резервирования
            $table->integer('reserved_quantity')->default(0)->after('quality_status');
            $table->integer('available_quantity')->storedAs('quantity - reserved_quantity')->after('reserved_quantity');
            
            // Информация о движении
            $table->datetime('last_movement_date')->nullable()->after('available_quantity');
            $table->string('storage_location')->nullable()->after('last_movement_date');
            
            // Индексы для оптимизации
            $table->index('lot_number');
            $table->index('expiry_date');
            $table->index('supplier_batch');
            $table->index('quality_status');
            $table->index('reserved_quantity');
            $table->index('available_quantity');
            $table->index('last_movement_date');
            $table->index('storage_location');
        });
    }

    public function down(): void
    {
        Schema::table('warehouse_items', function (Blueprint $table) {
            $table->dropIndex(['lot_number']);
            $table->dropIndex(['expiry_date']);
            $table->dropIndex(['supplier_batch']);
            $table->dropIndex(['quality_status']);
            $table->dropIndex(['reserved_quantity']);
            $table->dropIndex(['available_quantity']);
            $table->dropIndex(['last_movement_date']);
            $table->dropIndex(['storage_location']);
            
            $table->dropColumn([
                'lot_number',
                'expiry_date',
                'supplier_batch',
                'quality_status',
                'reserved_quantity',
                'available_quantity',
                'last_movement_date',
                'storage_location'
            ]);
        });
    }
};
